#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import sys
import os
import traceback
import customtkinter as ctk
import sqlite3
import datetime
from pathlib import Path
from language_manager import LanguageManager
import re

import calendar # Import calendar module for date operations

# New CalendarPopup class
class CalendarPopup(ctk.CTkToplevel):
    def __init__(self, parent, date_entry_widget, initial_date=None):
        super().__init__(parent)
        self.parent = parent
        self.date_entry_widget = date_entry_widget
        self.title(self.parent.language_manager.get_text("select_date_title"))
        self.geometry("300x350")
        self.transient(parent)
        self.grab_set()
        self.resizable(False, False)

        self.selected_date = initial_date if initial_date else datetime.date.today()
        self.current_month = self.selected_date.month
        self.current_year = self.selected_date.year

        self.create_widgets()
        self.update_calendar()

    def create_widgets(self):
        # Navigation Frame
        nav_frame = ctk.CTkFrame(self)
        nav_frame.pack(pady=10)

        self.prev_month_button = ctk.CTkButton(nav_frame, text="<", width=30, command=self.prev_month)
        self.prev_month_button.pack(side="left", padx=5)

        self.month_year_label = ctk.CTkLabel(nav_frame, text="", font=ctk.CTkFont(weight="bold"))
        self.month_year_label.pack(side="left", padx=10)

        self.next_month_button = ctk.CTkButton(nav_frame, text=">", width=30, command=self.next_month)
        self.next_month_button.pack(side="left", padx=5)

        # Weekday headers
        weekdays_frame = ctk.CTkFrame(self)
        weekdays_frame.pack()
        weekdays = [self.parent.language_manager.get_text(f"weekday_{i}") for i in range(7)] # French weekdays
        for i, day in enumerate(weekdays):
            label = ctk.CTkLabel(weekdays_frame, text=day, width=40, height=30, font=ctk.CTkFont(weight="bold"))
            label.grid(row=0, column=i, padx=1, pady=1)

        # Calendar grid
        self.calendar_frame = ctk.CTkFrame(self)
        self.calendar_frame.pack(pady=10)

    def update_calendar(self):
        for widget in self.calendar_frame.winfo_children():
            widget.destroy()

        self.month_year_label.configure(text=f"{self.get_month_name(self.current_month)} {self.current_year}")

        cal = calendar.Calendar()
        month_days = cal.monthdayscalendar(self.current_year, self.current_month)

        for week_idx, week in enumerate(month_days):
            for day_idx, day in enumerate(week):
                if day == 0:
                    label = ctk.CTkLabel(self.calendar_frame, text="", width=40, height=30)
                else:
                    date_obj = datetime.date(self.current_year, self.current_month, day)
                    button = ctk.CTkButton(self.calendar_frame, text=str(day), width=40, height=30,
                                           command=lambda d=date_obj: self.select_date(d))
                    if date_obj == self.selected_date:
                        button.configure(fg_color="blue") # Highlight selected date
                    elif date_obj == datetime.date.today():
                        button.configure(fg_color="gray") # Highlight today's date
                    label = button
                label.grid(row=week_idx, column=day_idx, padx=1, pady=1)

    def prev_month(self):
        self.current_month -= 1
        if self.current_month < 1:
            self.current_month = 12
            self.current_year -= 1
        self.update_calendar()

    def next_month(self):
        self.current_month += 1
        if self.current_month > 12:
            self.current_month = 1
            self.current_year += 1
        self.update_calendar()

    def select_date(self, date_obj):
        self.selected_date = date_obj
        self.date_entry_widget.set_date(self.selected_date)
        self.destroy()

    def get_month_name(self, month_num):
        # Simple mapping for French month names
        months = [self.parent.language_manager.get_text(f"month_{i}") for i in range(1, 13)]
        return months[month_num - 1]

# New CTkCalendarEntry class
class CTkCalendarEntry(ctk.CTkFrame):
    def __init__(self, parent, placeholder_text="Sélectionner une date 📅", **kwargs):
        super().__init__(parent, fg_color="transparent")
        self.grid_columnconfigure(0, weight=1) # Entry takes most space
        self.grid_columnconfigure(1, weight=0) # Button takes fixed space

        self.date_entry = ctk.CTkEntry(self, placeholder_text=placeholder_text, **kwargs)
        self.date_entry.grid(row=0, column=0, sticky="ew")
        
        # Bind key release to format input
        self.date_entry.bind('<KeyRelease>', self._format_date_input)
        self.date_entry.bind('<FocusOut>', self._validate_date_input)

        self.calendar_button = ctk.CTkButton(self, text="📅", width=30, command=self._open_calendar)
        self.calendar_button.grid(row=0, column=1, padx=(5, 0))

        self._selected_date_obj = None # Stores datetime.date object

    def _open_calendar(self):
        current_text = self.date_entry.get()
        initial_date = None
        try:
            # Try to parse DD/MM/YYYY
            if re.match(r"^\d{2}/\d{2}/\d{4}$", current_text):
                day, month, year = map(int, current_text.split('/'))
                initial_date = datetime.date(year, month, day)
            # Try to parse YYYY-MM-DD (from database format)
            elif re.match(r"^\d{4}-\d{2}-\d{2}$", current_text):
                year, month, day = map(int, current_text.split('-'))
                initial_date = datetime.date(year, month, day)
        except ValueError:
            pass # Keep initial_date as None if parsing fails

        CalendarPopup(self.master, self, initial_date)

    def set_date(self, date_obj):
        self._selected_date_obj = date_obj
        self.date_entry.delete(0, ctk.END)
        self.date_entry.insert(0, date_obj.strftime("%d/%m/%Y"))

    def get_date_string(self):
        # Returns date in DD/MM/YYYY format from the entry field
        return self.date_entry.get()

    def get_date_object(self):
        # Returns datetime.date object, or None if invalid
        date_str = self.date_entry.get()
        try:
            if re.match(r"^\d{2}/\d{2}/\d{4}$", date_str):
                day, month, year = map(int, date_str.split('/'))
                return datetime.date(year, month, day)
        except ValueError:
            pass
        return None

    # Expose get() method for compatibility with existing code
    def get(self):
        return self.get_date_string()

    def insert(self, index, text):
        self.date_entry.insert(index, text)
        self._format_date_input() # Format after insertion

    def delete(self, start, end):
        self.date_entry.delete(start, end)
        self._format_date_input() # Format after deletion

    def _format_date_input(self, event=None):
        current_text = self.date_entry.get()
        cursor_pos = self.date_entry.index(ctk.INSERT)

        # Allow only digits and '/'
        allowed_chars = '0123456789/'
        filtered_text = ''.join(char for char in current_text if char in allowed_chars)

        # Apply DD/MM/YYYY format
        numbers_only = ''.join(filter(str.isdigit, filtered_text))
        formatted = ""
        
        if len(numbers_only) > 0:
            formatted += numbers_only[:2]
            if len(numbers_only) > 2:
                formatted += '/' + numbers_only[2:4]
            if len(numbers_only) > 4:
                formatted += '/' + numbers_only[4:8]
        
        # Update text if changed
        if formatted != current_text:
            self.date_entry.delete(0, ctk.END)
            self.date_entry.insert(0, formatted)
            # Try to maintain cursor position
            new_cursor_pos = cursor_pos
            if cursor_pos > len(formatted):
                new_cursor_pos = len(formatted)
            self.date_entry.icursor(new_cursor_pos)

    def _validate_date_input(self, event=None):
        date_str = self.date_entry.get()
        if not date_str:
            return
        try:
            day, month, year = map(int, date_str.split('/'))
            datetime.date(year, month, day) # This will raise ValueError for invalid dates
            self.date_entry.configure(border_color=self.date_entry.cget("fg_color")) # Reset border color
        except ValueError:
            self.date_entry.configure(border_color="red") # Indicate invalid date
            print(self.parent.language_manager.get_text("invalid_date_error").format(date_str=date_str))

# Helper function to create the new date entry
def create_enhanced_date_entry(parent, placeholder_text="Sélectionner une date 📅", allow_keyboard_input=True, **kwargs):
    """Créer un champ de date amélioré avec calendrier et prise en charge de la saisie au clavier"""
    date_entry = CTkCalendarEntry(parent, placeholder_text=placeholder_text, **kwargs)
    return date_entry

# إعداد المسار النسبي لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DB_PATH.mkdir(exist_ok=True)
DATABASE_FILE = DB_PATH / "accounting.db"

def check_dependencies():
    """Vérification des bibliothèques requises"""
    print("🔍 Vérification des bibliothèques requises...")

    try:
        import customtkinter
        # استخدم مدير اللغة مباشرة بدلاً من app
        language_manager = LanguageManager()
        print(language_manager.get_text("customtkinter_available"))
        return True
    except ImportError:
        language_manager = LanguageManager()
        print(language_manager.get_text("customtkinter_not_found"))
        print(language_manager.get_text("install_customtkinter_prompt"))
        print("   pip install customtkinter")
        return False

class AccountingApp(ctk.CTk):

    def create_recent_invoices_table(self, parent):
        """عرض جدول آخر 10 فواتير في لوحة القيادة"""
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ["Numéro", "Client", "Date", "Montant TTC", "Statut"]
        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        invoices = self.get_recent_invoices()
        for row_idx, invoice in enumerate(invoices, start=1):
            (id, numero, client, date_facture, montant_ttc, statut) = invoice
            display_values = [
                numero or "",
                client or "",
                self.format_date(date_facture),
                f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH",
                statut or "En attente"
            ]
            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")
    
    def __init__(self):
        super().__init__()

        # Créer le gestionnaire de langues
        self.language_manager = LanguageManager()

        # Configurer la devise
        self.currency = self.language_manager.get_text("currency")

        self.title(self.language_manager.get_text("app_title"))
        self.geometry("1400x900")

        self.db_path = str(DATABASE_FILE)
        self.setup_database()

        # Configurer la grille principale
        self.grid_columnconfigure(0, weight=0)  # Colonne de la barre latérale
        self.grid_columnconfigure(1, weight=1)  # Colonne du contenu principal
        self.grid_rowconfigure(0, weight=1)

        # Variables pour stocker les références des éléments
        self.sidebar_frame = None
        self.main_content_frame = None

        self.setup_ui()
        self.show_dashboard()

    def format_currency(self, amount):
        """Formater le montant avec la devise locale"""
        try:
            if isinstance(amount, str):
                amount = float(amount.replace(',', '.').replace(' ', '').replace(self.currency, ''))
            return f"{amount:.2f} {self.currency}"
        except (ValueError, TypeError):
            return f"0.00 {self.currency}"
    
    def format_date(self, date_str):
        """Formater la date pour l'affichage"""
        if not date_str:
            return self.language_manager.get_text("not_specified")
        
        try:
            # Si la date est au format timestamp complet
            if ' ' in str(date_str):
                date_part = str(date_str).split(' ')[0]  # Prendre seulement la partie date
            else:
                date_part = str(date_str)
            
            # Convertir la date de YYYY-MM-DD en DD/MM/YYYY
            if '-' in date_part and len(date_part.split('-')) == 3:
                year, month, day = date_part.split('-')
                return f"{day}/{month}/{year}"
            
            # Si la date est dans un autre format, la retourner telle quelle
            return date_part
            
        except (ValueError, AttributeError, IndexError):
            return self.language_manager.get_text("invalid_date")
    
    def format_date_for_input(self, date_str):
        """Formater la date pour les champs de saisie (DD/MM/YYYY)"""
        if not date_str:
            return "" # Retourner une chaîne vide pour un placeholder
        
        try:
            # Si la date est au format timestamp complet
            if ' ' in str(date_str):
                date_part = str(date_str).split(' ')[0]
            else:
                date_part = str(date_str)
            
            # Convertir de YYYY-MM-DD en DD/MM/YYYY
            if '-' in date_part and len(date_part.split('-')) == 3:
                year, month, day = date_part.split('-')
                return f"{day.zfill(2)}/{month.zfill(2)}/{year}"
            
            # Si la date est déjà au format DD/MM/YYYY
            if '/' in date_part and len(date_part.split('/')) == 3:
                return date_part
            
            return "" # Retourner une chaîne vide si le format n'est pas reconnu
            
        except (ValueError, AttributeError, IndexError):
            return "" # Retourner une chaîne vide en cas d'erreur
    
    def parse_date_input(self, date_input):
        """Convertir une date d'un champ de saisie au format de la base de données"""
        if not date_input: # Si le champ est vide
            return None
        
        try:
            # Supprimer les caractères indésirables
            clean_input = date_input.replace('_', '').strip()
            
            # Si la date est au format DD/MM/YYYY
            if '/' in clean_input:
                parts = clean_input.split('/')
                if len(parts) == 3 and all(part.isdigit() for part in parts):
                    day, month, year = parts
                    # Valider la date
                    # Note: datetime.date() fera une validation plus robuste
                    datetime.date(int(year), int(month), int(day))
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            return None
            
        except (ValueError, AttributeError, IndexError):
            return None

    def get_family_abbreviation(self, family_name):
        """Convertir le nom de la famille en abréviation (première lettre de chaque mot)"""
        if not family_name or family_name == self.language_manager.get_text("no_family"):
            return "--"

        # Diviser le texte en mots
        words = family_name.strip().split()

        if not words:
            return "--"

        # إذا كانت كلمة واحدة: الحرف الأول فقط
        if len(words) == 1:
            return words[0][0].upper()

        # إذا كانت عدة كلمات: الحرف الأول من كل كلمة
        abbreviation = ""
        for word in words:
            if word:  # تجاهل الكلمات الفارغة
                abbreviation += word[0].upper()

        return abbreviation if abbreviation else "--"

    def renumber_all_products(self):
        """Renumeroter tous les produits automatiquement par ordre sequentiel par famille"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Obtenir tous les produits avec leurs familles
            cursor.execute("""
                SELECT p.id, p.code, f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY f.nom, p.id ASC
            """)
            products = cursor.fetchall()

            # Regrouper les produits par famille
            family_counters = {}

            for product_id, old_code, famille_nom in products:
                # Définir le préfixe
                prefix = self.get_family_prefix(famille_nom)

                # Incrémenter le compteur pour cette famille
                if prefix not in family_counters:
                    family_counters[prefix] = 0
                family_counters[prefix] += 1

                # Créer le nouveau code
                new_code = f"{prefix}{family_counters[prefix]:03d}"

                if old_code != new_code:
                    cursor.execute("UPDATE produits SET code = ? WHERE id = ?", (new_code, product_id))
                    print(self.language_manager.get_text("product_code_change").format(old_code=old_code, new_code=new_code))

            conn.commit()
            conn.close()
            print(f"✅ تم إعادة ترقيم {len(products)} منتج بنجاح")

        except Exception as e:
            print(self.language_manager.get_text("renumbering_error").format(error=e))
            if conn:
                conn.close()

    def renumber_all_clients(self):
        """Renumeroter tous les clients automatiquement par ordre sequentiel"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Obtenir tous les clients triés par ID
            cursor.execute("SELECT id, code FROM clients ORDER BY id ASC")
            clients = cursor.fetchall()

            # Renumeroter chaque client
            for index, (client_id, old_code) in enumerate(clients, start=1):
                new_code = f"C{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE clients SET code = ? WHERE id = ?", (new_code, client_id))
                    print(self.language_manager.get_text("client_code_change").format(old_code=old_code, new_code=new_code))

            conn.commit()
            conn.close()
            print(self.language_manager.get_text("clients_renumbered_success").format(count=len(clients)))

        except Exception as e:
            print(self.language_manager.get_text("client_renumbering_error").format(error=e))
            if conn:
                conn.close()

    def generate_next_supplier_code(self):
        """Générer le prochain code fournisseur automatiquement (F001, F002, etc.)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Obtenir le dernier code fournisseur
            cursor.execute("SELECT code FROM fournisseurs WHERE code LIKE 'F%' ORDER BY code DESC LIMIT 1")
            result = cursor.fetchone()

            if result:
                last_code = result[0]
                # Extraire le numéro du code (ex: F001 -> 001)
                try:
                    last_number = int(last_code[1:])  # Enlever le 'F' et convertir en entier
                    next_number = last_number + 1
                except (ValueError, IndexError):
                    next_number = 1
            else:
                next_number = 1

            conn.close()
            return f"F{next_number:03d}"

        except Exception as e:
            print(f"Erreur lors de la génération du code fournisseur: {e}")
            return "F001"  # Code par défaut en cas d'erreur

    def renumber_all_suppliers(self):
        """Renumeroter tous les fournisseurs automatiquement par ordre sequentiel"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Obtenir tous les fournisseurs triés par ID
            cursor.execute("SELECT id, code FROM fournisseurs ORDER BY id ASC")
            suppliers = cursor.fetchall()

            # Renumeroter chaque fournisseur
            for index, (supplier_id, old_code) in enumerate(suppliers, start=1):
                new_code = f"F{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE fournisseurs SET code = ? WHERE id = ?", (new_code, supplier_id))
                    print(self.language_manager.get_text("supplier_code_change").format(old_code=old_code, new_code=new_code))

            conn.commit()
            conn.close()
            print(self.language_manager.get_text("suppliers_renumbered_success").format(count=len(suppliers)))

        except Exception as e:
            print(self.language_manager.get_text("supplier_renumbering_error").format(error=e))
            if conn:
                conn.close()

    def assign_codes_to_existing_suppliers(self):
        """Assigner des codes aux fournisseurs existants qui n'en ont pas"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Obtenir les fournisseurs sans code
            cursor.execute("SELECT id FROM fournisseurs WHERE code IS NULL OR code = ''")
            suppliers_without_codes = cursor.fetchall()

            if suppliers_without_codes:
                print(f"Attribution de codes à {len(suppliers_without_codes)} fournisseurs...")

                # Renumeroter tous les fournisseurs pour assurer la cohérence
                self.renumber_all_suppliers()

                print("✅ Codes attribués avec succès à tous les fournisseurs")
            else:
                print("✅ Tous les fournisseurs ont déjà des codes")

            conn.close()

        except Exception as e:
            print(f"❌ Erreur lors de l'attribution des codes: {e}")
            if conn:
                conn.close()



    def setup_ui(self):
        """Configurer l'interface utilisateur en fonction de la langue"""
        # Supprimer les éléments existants si présents
        if self.sidebar_frame:
            self.sidebar_frame.destroy()
        if self.main_content_frame:
            self.main_content_frame.destroy()

        # Barre latérale à gauche (français uniquement)
        sidebar_column = 0
        main_column = 1
        sidebar_sticky = self.language_manager.get_sticky_for_language()

        # Créer la barre latérale
        self.sidebar_frame = ctk.CTkFrame(self, width=250, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=sidebar_column, rowspan=4, sticky=sidebar_sticky, padx=0, pady=0)
        self.sidebar_frame.grid_rowconfigure(15, weight=1)  # Augmenter le nombre pour accueillir tous les éléments
        self.sidebar_frame.grid_propagate(False)  # Empêcher l'expansion de la barre latérale

        # Créer le contenu principal
        self.main_content_frame = ctk.CTkFrame(self)
        self.main_content_frame.grid(row=0, column=main_column, sticky="nsew", padx=0, pady=0)
        self.main_content_frame.grid_columnconfigure(0, weight=1)
        self.main_content_frame.grid_rowconfigure(0, weight=1)

        self.create_sidebar()

    def setup_database(self):
        """Créer la base de données et les tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Table Clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                categorie TEXT,
                client TEXT NOT NULL,
                ice TEXT,
                if_client TEXT,
                adresse TEXT,
                personne_contacter TEXT,
                contact TEXT,
                n_fix TEXT,
                n_fax TEXT,
                email TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table Fournisseurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                nom TEXT NOT NULL,
                telephone TEXT,
                adresse TEXT,
                ice TEXT,
                if_fournisseur TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table Familles
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS familles_produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT UNIQUE NOT NULL,
                description TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Table Produits
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                designation TEXT NOT NULL,
                prix_achat REAL DEFAULT 0,
                prix_vente REAL DEFAULT 0,
                qte_achat INTEGER DEFAULT 0,
                stock INTEGER DEFAULT 0,
                fournisseur TEXT,
                famille_id INTEGER,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (famille_id) REFERENCES familles_produits (id)
            )
        ''')

        # Table Factures
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE NOT NULL,
                client_id INTEGER,
                date_facture DATE,
                montant_ht REAL DEFAULT 0,
                montant_tva REAL DEFAULT 0,
                montant_ttc REAL DEFAULT 0,
                statut TEXT DEFAULT 'En attente',
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # Table Marchés - mise à jour selon les spécifications fonctionnelles
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS marches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_marche TEXT DEFAULT 'DC',
                devis_numero TEXT,
                numero_manuel TEXT,
                nature_prestation TEXT DEFAULT 'Travaux',
                objet TEXT,
                delai_execution TEXT,
                client_id INTEGER,
                montant_ttc REAL DEFAULT 0,
                caution_provisoire REAL DEFAULT 0,
                date_notification_approbation DATE,
                caution_definitif REAL DEFAULT 0,
                ordre_service DATE,
                caution_retenu_garantie REAL DEFAULT 0,
                date_prevu_achevement DATE,
                total_achat_ht REAL DEFAULT 0,
                total_vente_ht REAL DEFAULT 0,
                total_marge_ht REAL DEFAULT 0,
                total_tva REAL DEFAULT 0,
                tva_rate REAL DEFAULT 20.0,
                reste_beneficiaire REAL DEFAULT 0,
                statut TEXT DEFAULT 'En cours',
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # Table Bordereau de Prix - 9 colonnes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS marche_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER NOT NULL,
                numero INTEGER NOT NULL,
                designation TEXT NOT NULL,
                unite TEXT DEFAULT 'unité',
                quantite REAL DEFAULT 1,
                prix_achat_ht REAL DEFAULT 0,
                total_achat_ht REAL DEFAULT 0,
                prix_u_ht REAL DEFAULT 0,
                total_vente_ht REAL DEFAULT 0,
                marge REAL DEFAULT 0,
                FOREIGN KEY (marche_id) REFERENCES marches (id) ON DELETE CASCADE
            )
        ''')

        # Table Frais supplémentaires (Transport & Autres) - 10 champs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS marche_frais (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER NOT NULL,
                type_frais TEXT NOT NULL,
                code_comptable TEXT,
                montant REAL DEFAULT 0,
                mode_paiement TEXT DEFAULT 'selectionner mode de paiement',
                ajouter_caisse BOOLEAN DEFAULT FALSE,
                description TEXT,
                FOREIGN KEY (marche_id) REFERENCES marches (id) ON DELETE CASCADE
            )
        ''')

        # Ajouter la colonne famille_id si elle n'existe pas
        try:
            cursor.execute("ALTER TABLE produits ADD COLUMN famille_id INTEGER")
            print(self.language_manager.get_text("column_added").format(column_name="famille_id"))
        except sqlite3.OperationalError:
            # La colonne existe déjà
            pass

        # Ajouter la colonne qte_achat si elle n'existe pas
        try:
            cursor.execute("ALTER TABLE produits ADD COLUMN qte_achat INTEGER DEFAULT 0")
            print(self.language_manager.get_text("column_added").format(column_name="qte_achat"))
        except sqlite3.OperationalError:
            # La colonne existe déjà
            pass

        # Ajouter les colonnes supplémentaires requises pour la fenêtre de modification
        additional_columns = [
            ("date_achat", "TEXT", self.language_manager.get_text("purchase_date")),
            ("facture_achat", "TEXT", self.language_manager.get_text("purchase_invoice_number")),
            ("tva_rate", "REAL DEFAULT 20.0", self.language_manager.get_text("tax_rate")),
            ("mode_paiement", "TEXT DEFAULT 'carte bancaire'", self.language_manager.get_text("payment_method")),
            ("date_paiement", "TEXT", self.language_manager.get_text("payment_date"))
        ]

        for column_name, column_type, description in additional_columns:
            try:
                cursor.execute(f"ALTER TABLE produits ADD COLUMN {column_name} {column_type}")
                print(self.language_manager.get_text("column_added_with_desc").format(description=description, column_name=column_name))
            except sqlite3.OperationalError:
                # La colonne existe déjà
                pass

        # Ajouter la colonne 'code' aux tables existantes si elle n'existe pas
        try:
            # Vérifier et ajouter la colonne 'code' à la table clients
            cursor.execute("PRAGMA table_info(clients)")
            clients_columns = [column[1] for column in cursor.fetchall()]
            if 'code' not in clients_columns:
                cursor.execute("ALTER TABLE clients ADD COLUMN code TEXT")
                print(self.language_manager.get_text("code_column_added_clients"))

                # Ajouter des codes aux clients existants
                cursor.execute("SELECT id FROM clients ORDER BY id ASC")
                clients = cursor.fetchall()
                for index, (client_id,) in enumerate(clients, start=1):
                    cursor.execute("UPDATE clients SET code = ? WHERE id = ?", (f"C{index:03d}", client_id))
                print(self.language_manager.get_text("codes_added_clients").format(count=len(clients)))

            # Vérifier et ajouter la colonne 'code' à la table fournisseurs
            cursor.execute("PRAGMA table_info(fournisseurs)")
            suppliers_columns = [column[1] for column in cursor.fetchall()]
            if 'code' not in suppliers_columns:
                cursor.execute("ALTER TABLE fournisseurs ADD COLUMN code TEXT")
                print(self.language_manager.get_text("code_column_added_suppliers"))

                # Ajouter des codes aux fournisseurs existants
                cursor.execute("SELECT id FROM fournisseurs ORDER BY id ASC")
                suppliers = cursor.fetchall()
                for index, (supplier_id,) in enumerate(suppliers, start=1):
                    cursor.execute("UPDATE fournisseurs SET code = ? WHERE id = ?", (f"F{index:03d}", supplier_id))
                print(self.language_manager.get_text("codes_added_suppliers").format(count=len(suppliers)))

        except Exception as e:
            print(self.language_manager.get_text("code_column_add_error").format(error=e))

        conn.commit()
        conn.close()
        print(self.language_manager.get_text("database_ready"))

        # Assigner des codes aux fournisseurs existants qui n'en ont pas
        self.assign_codes_to_existing_suppliers()

        # Ajouter des données d'exemple si elles n'existent pas
        self.add_sample_data()

    def create_sidebar(self):
        """Créer la barre latérale de navigation"""
        # Titre de l'application
        logo_label = ctk.CTkLabel(
            self.sidebar_frame,
            text=self.language_manager.get_text("app_title"),
            font=ctk.CTkFont(size=20, weight="bold")
        )
        logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))

        # Ordre des sections en français
        nav_buttons = [
            ("dashboard", self.show_dashboard),
            ("clients", self.show_clients_page),
            ("suppliers", self.show_suppliers_page),
            ("products", self.show_products_page),
            ("devis", self.show_devis_page),
            ("invoices", self.show_invoices_page),
            ("purchase_orders", self.show_purchase_orders_page),
            ("delivery_notes", self.show_delivery_notes_page),
            ("marche", self.show_marche_page),
            ("stock", self.show_stock_page),
            ("cash", self.show_cash_page),
            ("tva", self.show_tva_page),
            ("reports", self.show_reports_page),
            ("search", self.show_search_page)
        ]

        # Créer les boutons de navigation
        for i, (key, command) in enumerate(nav_buttons, start=1):
            text = self.language_manager.get_text(key)
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=command,
                anchor="center"
            )
            button.grid(row=i, column=0, padx=20, pady=10, sticky="ew")





    def clear_main_content(self):
        """Effacer le contenu du cadre principal"""
        for widget in self.main_content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        self.clear_main_content()
 
        # Alignement du texte à gauche (français)
        anchor = self.language_manager.get_anchor_for_language()

        title_label = ctk.CTkLabel(
            self.main_content_frame,
            text=self.language_manager.get_text("dashboard"),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(anchor=anchor, padx=20, pady=(20, 10))

        # Statistiques rapides
        stats_frame = ctk.CTkFrame(self.main_content_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        clients_count = self.get_clients_count()
        products_count = self.get_products_count()
        invoices_count = self.get_invoices_count()
        suppliers_count = self.get_suppliers_count()

        # Ordre des cartes selon la langue
        column_order = self.language_manager.get_column_order(4) # Obtenir l'ordre des colonnes
        
        self.create_stat_card(stats_frame, self.language_manager.get_text("total_invoices"), invoices_count, "#a855f7", column_order[0])
        self.create_stat_card(stats_frame, self.language_manager.get_text("total_products"), products_count, "#3b82f6", column_order[1])
        self.create_stat_card(stats_frame, self.language_manager.get_text("total_clients"), clients_count, "#22c55e", column_order[2])
        self.create_stat_card(stats_frame, self.language_manager.get_text("total_suppliers"), suppliers_count, "#f59e0b", column_order[3])

        # Tableau des dernières factures
        invoices_frame = ctk.CTkFrame(self.main_content_frame)
        invoices_frame.pack(fill="both", expand=True, padx=20, pady=10)

        invoices_title_text = self.language_manager.get_text("latest_invoices")
        invoices_title_label = ctk.CTkLabel(
            invoices_frame,
            text=invoices_title_text,
            font=ctk.CTkFont(size=18, weight="bold")
        )
        invoices_title_label.pack(pady=(10, 5), anchor=anchor)

        self.create_recent_invoices_table(invoices_frame)

    def show_clients_page(self):
        self.clear_main_content()
 
        # Alignement du texte à gauche (français)
        anchor = self.language_manager.get_anchor_for_language()

        # Titre de la page
        title_text = self.language_manager.get_text("clients")
        title_label = ctk.CTkLabel(
            self.main_content_frame,
            text=self.language_manager.get_text("manage_clients"),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(anchor=anchor, padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=20, pady=10)

        # Ordre des éléments en français
        search_side = "left"
        buttons_side = "right"
        search_placeholder = self.language_manager.get_text("search_clients_placeholder")

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=search_placeholder)
        search_entry.pack(side=search_side, fill="x", expand=True, padx=5, pady=5)

        # Bouton d'ajout
        add_text = self.language_manager.get_text("add") + " " + self.language_manager.get_text("client_singular")
        add_button = ctk.CTkButton(toolbar_frame, text=add_text, command=self.show_add_client_dialog)
        add_button.pack(side=buttons_side, padx=5, pady=5)

        # Tableau des clients
        self.create_clients_table(self.main_content_frame)

    def show_suppliers_page(self):
        self.clear_main_content()
 
        # Titre de la page
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("manage_suppliers"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_suppliers_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("add_supplier"), command=self.show_add_supplier_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des fournisseurs
        self.create_suppliers_table(self.main_content_frame)

    def show_products_page(self):
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("manage_products"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_products_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("add_product"), command=self.show_add_product_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des produits
        self.create_products_table(self.main_content_frame)
        
        # Mettre à jour l'interface pour assurer un affichage correct
        self.update_idletasks()
        print(self.language_manager.get_text("products_page_recreated"))



    def show_invoices_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("manage_invoices"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_invoices_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("new_invoice"), command=self.show_add_invoice_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des factures
        self.create_invoices_table(self.main_content_frame)

    def show_purchase_orders_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("manage_purchase_orders"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_purchase_orders_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("new_purchase_order"), command=self.show_add_purchase_order_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des bons de commande
        self.create_purchase_orders_table(self.main_content_frame)

    def show_delivery_notes_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("delivery_note_title"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_delivery_notes_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("new_delivery_note"), command=self.show_enhanced_delivery_note_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des bons de livraison amélioré
        self.create_enhanced_delivery_notes_table(self.main_content_frame)

    def show_devis_page(self):
        self.clear_main_content()
 
        # Titre de la page
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("manage_devis"), font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_devis_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("new_devis"), command=self.show_add_devis_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des devis
        self.create_devis_table(self.main_content_frame)

    def create_devis_table(self, parent):
        """Créer le tableau des devis"""
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Numéro", "Client", "Date", "Montant HT", "TVA", "Montant TTC", "Statut"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        # Exemple de données de devis
        sample_devis = [
            (1, "DEV001", "Client A", "2024-01-15", "10000.00", "2000.00", "12000.00", "En attente"),
            (2, "DEV002", "Client B", "2024-01-10", "5000.00", "1000.00", "6000.00", "Accepté")
        ]

        # Stocker les données des devis pour le menu contextuel
        self.devis_table_data = {}

        for row_idx, devis in enumerate(sample_devis, start=1):
            devis_id, numero, client, date, montant_ht, tva, montant_ttc, statut = devis
 
            # Stocker les données du devis
            self.devis_table_data[row_idx] = {
                'id': devis_id,
                'numero': numero,
                'client': client,
                'date': date,
                'montant_ht': montant_ht,
                'tva': tva,
                'montant_ttc': montant_ttc,
                'statut': statut
            }

            display_values = [numero, client, date, montant_ht, tva, montant_ttc, statut]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # Ajouter le menu contextuel à la cellule
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_devis_context_menu(e, row))

        # Ajouter le menu contextuel au cadre également
        table_frame.bind("<Button-3>", self.show_general_devis_context_menu)

    def show_marche_page(self):
        """Afficher la page de gestion des marchés - tableau des marchés uniquement"""
        title_label = ctk.CTkLabel(self.main_content_frame, text=self.language_manager.get_text("marche_management"),
                                 font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Barre d'outils
        toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        toolbar_frame.pack(fill="x", padx=10, pady=5)

        search_entry = ctk.CTkEntry(toolbar_frame, placeholder_text=self.language_manager.get_text("search_marches_placeholder"))
        search_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

        add_button = ctk.CTkButton(toolbar_frame, text=self.language_manager.get_text("new_marche"),
                                 command=self.show_add_marche_dialog)
        add_button.pack(side="right", padx=5, pady=5)

        # Tableau des marchés uniquement
        self.create_marche_table(self.main_content_frame)

    def create_marche_general_info_section(self, parent):
        """Créer la section des informations générales du marché - 14 champs"""
        # Cadre des informations générales
        general_frame = ctk.CTkFrame(parent)
        general_frame.pack(fill="x", padx=5, pady=5)
 
        # Titre de la section
        section_title = ctk.CTkLabel(general_frame, text=self.language_manager.get_text("marche_general_info"),
                                   font=ctk.CTkFont(size=18, weight="bold"))
        section_title.pack(anchor="w", padx=10, pady=(10, 5))

        # Cadre des champs - grille 4x4
        fields_frame = ctk.CTkFrame(general_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # Initialiser le dictionnaire des champs
        self.marche_fields = {}

        # Première ligne - Type de marché (liste de sélection) et N° DEVIS
        self.create_marche_type_field(fields_frame, 0, 0)
        self.create_field_with_label(fields_frame, self.language_manager.get_text("devis_numero_label"), self.language_manager.get_text("select_or_type"), 0, 2, "devis_numero")

        # Deuxième ligne - N et Nature de prestation (liste de sélection)
        self.create_field_with_label(fields_frame, self.language_manager.get_text("numero_label"), self.language_manager.get_text("manual"), 1, 0, "numero_manuel")
        self.create_nature_prestation_field(fields_frame, 1, 2)

        # Troisième ligne - Objet et Délai d'exécution
        self.create_field_with_label(fields_frame, self.language_manager.get_text("object_label"), self.language_manager.get_text("automatic"), 2, 0, "objet")
        self.create_field_with_label(fields_frame, self.language_manager.get_text("execution_delay_label"), self.language_manager.get_text("three_months"), 2, 2, "delai_execution")

        # Quatrième ligne - Client et Montant TTC
        self.create_client_field(fields_frame, 3, 0)
        montant_entry = self.create_field_with_label(fields_frame, self.language_manager.get_text("montant_ttc_label"), "0.00", 3, 2, "montant_ttc")
        # Lier le changement de montant au calcul de la caution définitive
        montant_entry.bind("<KeyRelease>", lambda e: self.calculate_caution_definitif())

        # Cinquième ligne - Caution provisoire et Date de notification d'approbation
        self.create_field_with_label(fields_frame, self.language_manager.get_text("provisional_caution_label"), "0.00", 4, 0, "caution_provisoire")
        self.create_date_field(fields_frame, self.language_manager.get_text("notification_date_label"), 4, 2, "date_notification_approbation")

        # Sixième ligne - Caution définitive (calcul automatique) et Ordre de service
        self.create_calculated_field(fields_frame, self.language_manager.get_text("definitive_caution_label"), "0.00", 5, 0, "caution_definitif")
        ordre_entry = self.create_date_field(fields_frame, self.language_manager.get_text("service_order_label"), 5, 2, "ordre_service")
        # Lier le changement de date de l'ordre de service au calcul de la date d'achèvement
        ordre_entry.bind("<KeyRelease>", lambda e: self.calculate_date_achevement())

        # Septième ligne - Caution retenue de garantie (calcul automatique) et Date prévue d'achèvement
        self.create_calculated_field(fields_frame, self.language_manager.get_text("retention_caution_label"), "0.00", 6, 0, "caution_retenu_garantie")
        self.create_calculated_field(fields_frame, self.language_manager.get_text("completion_date_label"), self.language_manager.get_text("auto"), 6, 2, "date_prevu_achevement")

    def create_field_with_label(self, parent, label_text, default_value, row, col, field_key=None):
        """Créer un champ avec une étiquette"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=label_text, anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Champ
        entry = ctk.CTkEntry(parent, placeholder_text=default_value)
        entry.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Enregistrer la référence du champ
        if field_key:
            self.marche_fields[field_key] = entry

        return entry

    def create_marche_type_field(self, parent, row, col):
        """Créer le champ de type de marché - liste de sélection entre DC et marché"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=self.language_manager.get_text("type_marche"), anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Liste de sélection
        type_combo = ctk.CTkComboBox(parent, values=["DC", "marché"], state="readonly")
        type_combo.set("DC")  # Valeur par défaut
        type_combo.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Enregistrer la référence du champ
        self.marche_fields['type_marche'] = type_combo

        return type_combo

    def create_nature_prestation_field(self, parent, row, col):
        """Créer le champ de nature de prestation - liste de sélection entre Travaux et fourniture"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=self.language_manager.get_text("nature_prestation"), anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Liste de sélection
        nature_combo = ctk.CTkComboBox(parent, values=["Travaux", "fourniture"], state="readonly")
        nature_combo.set("Travaux")  # Valeur par défaut
        nature_combo.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Lier le changement de valeur au calcul de la caution de retenue
        nature_combo.configure(command=self.calculate_caution_retenu)

        # Enregistrer la référence du champ
        self.marche_fields['nature_prestation'] = nature_combo

        return nature_combo

    def create_client_field(self, parent, row, col):
        """Créer le champ client - liste de sélection de la base de données"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=self.language_manager.get_text("client"), anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Récupérer la liste des clients
        clients_list = self.get_clients_list()
 
        # Liste de sélection
        client_combo = ctk.CTkComboBox(parent, values=clients_list, state="readonly")
        if clients_list:
            client_combo.set(clients_list[0])  # Valeur par défaut
        client_combo.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Enregistrer la référence du champ
        self.marche_fields['client'] = client_combo

        return client_combo

    def create_date_field(self, parent, label_text, row, col, field_key=None):
        """Créer un champ de date amélioré avec calendrier"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=label_text, anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Champ de date amélioré
        date_entry = create_enhanced_date_entry(parent, self.language_manager.get_text("select_date_placeholder"))
        date_entry.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Enregistrer la référence du champ
        if field_key:
            self.marche_fields[field_key] = date_entry

        return date_entry

    def create_calculated_field(self, parent, label_text, default_value, row, col, field_key=None):
        """Créer un champ calculé automatiquement"""
        # Étiquette
        label = ctk.CTkLabel(parent, text=label_text, anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        # Champ calculé (lecture seule)
        calc_entry = ctk.CTkEntry(parent, placeholder_text=default_value, state="disabled")
        calc_entry.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        # Enregistrer la référence du champ
        if field_key:
            self.marche_fields[field_key] = calc_entry

        return calc_entry

    def get_clients_list(self):
        """Récupérer la liste des clients de la base de données"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT nom FROM clients ORDER BY nom")
            clients = [row[0] for row in cursor.fetchall()]
            conn.close()
            return clients if clients else [self.language_manager.get_text("no_client")]
        except Exception as e:
            print(self.language_manager.get_text("error_fetching_clients").format(error=e))
            return [self.language_manager.get_text("error")]

    def calculate_caution_retenu(self, value=None):
        """Calculer la caution de retenue en fonction de la nature de la prestation"""
        try:
            if 'nature_prestation' in self.marche_fields and 'caution_retenu_garantie' in self.marche_fields:
                nature = self.marche_fields['nature_prestation'].get()
                caution_field = self.marche_fields['caution_retenu_garantie']

                # حساب النسبة: 7% للأعمال، 0% للتوريد
                if nature == "Travaux":
                    percentage = 7
                else:  # fourniture
                    percentage = 0

                # تحديث الحقل
                caution_field.configure(state="normal")
                caution_field.delete(0, "end")
                caution_field.insert(0, f"{percentage}%")
                caution_field.configure(state="disabled")

        except Exception as e:
            print(f"❌ خطأ في حساب ضمان الاحتفاظ: {e}")

    def calculate_caution_definitif(self):
        """حساب الضمان النهائي (2% من المبلغ الإجمالي)"""
        try:
            if 'montant_ttc' in self.marche_fields and 'caution_definitif' in self.marche_fields:
                montant_str = self.marche_fields['montant_ttc'].get()
                if montant_str:
                    montant = float(montant_str)
                    caution = round(montant * 0.02, 2)  # 2% مع التقريب

                    caution_field = self.marche_fields['caution_definitif']
                    caution_field.configure(state="normal")
                    caution_field.delete(0, "end")
                    caution_field.insert(0, f"{caution:.2f}")
                    caution_field.configure(state="disabled")

        except Exception as e:
            print(f"❌ خطأ في حساب الضمان النهائي: {e}")

    def calculate_date_achevement(self):
        """حساب تاريخ الانتهاء المتوقع"""
        try:
            if ('ordre_service' in self.marche_fields and
                'delai_execution' in self.marche_fields and
                'date_prevu_achevement' in self.marche_fields):

                ordre_service_str = self.marche_fields['ordre_service'].get()
                delai_str = self.marche_fields['delai_execution'].get()

                if ordre_service_str and delai_str:
                    # تحويل التاريخ والمدة (مبسط)
                    from datetime import datetime, timedelta

                    ordre_date = datetime.strptime(ordre_service_str, "%Y-%m-%d")

                    # استخراج عدد الأشهر من النص
                    import re
                    months_match = re.search(r'(\d+)', delai_str)
                    if months_match:
                        months = int(months_match.group(1))
                        # إضافة الأشهر (تقريبي: 30 يوم للشهر)
                        achevement_date = ordre_date + timedelta(days=months * 30)

                        achevement_field = self.marche_fields['date_prevu_achevement']
                        achevement_field.configure(state="normal")
                        achevement_field.delete(0, "end")
                        achevement_field.insert(0, achevement_date.strftime("%Y-%m-%d"))
                        achevement_field.configure(state="disabled")

        except Exception as e:
            print(f"❌ خطأ في حساب تاريخ الانتهاء: {e}")

    def create_marche_bordereau_section(self, parent):
        """إنشاء قسم بنود الأسعار (Bordereau de Prix)"""
        # إطار بنود الأسعار
        bordereau_frame = ctk.CTkFrame(parent)
        bordereau_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # عنوان القسم
        section_title = ctk.CTkLabel(bordereau_frame, text=self.language_manager.get_text("marche_bordereau"),
                                   font=ctk.CTkFont(size=18, weight="bold"))
        section_title.pack(anchor="w", padx=10, pady=(10, 5))

        # إطار الجدول
        table_frame = ctk.CTkScrollableFrame(bordereau_frame, height=300)
        table_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # رؤوس الأعمدة - 9 أعمدة حسب المواصفات
        headers = [
            "N°",                    # الرقم التسلسلي للبند
            "Désignation",           # وصف البند/الخدمة
            "U",                      # وحدة القياس
            "Qte",                    # الكمية
            "PRIX ACHAT HT",          # سعر الشراء للوحدة بدون ضريبة
            "TOTAL ACHAT HT",         # إجمالي سعر الشراء بدون ضريبة
            "Prix U HT",              # سعر البيع للوحدة بدون ضريبة
            "TOTAL VENT HT",          # إجمالي سعر البيع بدون ضريبة
            "MARGE"                   # الهامش (الربح)
        ]

        # إنشاء رؤوس الأعمدة مع ضبط العرض
        for i, header in enumerate(headers):
            header_label = ctk.CTkLabel(table_frame, text=header,
                                      font=ctk.CTkFont(weight="bold", size=12))
            header_label.grid(row=0, column=i, padx=2, pady=5, sticky="ew")
            # ضبط عرض الأعمدة حسب المحتوى
            if i == 0:  # N°
                table_frame.grid_columnconfigure(i, weight=0, minsize=40)
            elif i == 1:  # Désignation
                table_frame.grid_columnconfigure(i, weight=3, minsize=150)
            elif i == 2:  # U
                table_frame.grid_columnconfigure(i, weight=1, minsize=60)
            else:  # باقي الأعمدة
                table_frame.grid_columnconfigure(i, weight=2, minsize=100)

        # صفوف البيانات الافتراضية - صفين حسب المواصفات
        for row in range(1, 3):
            self.create_bordereau_row(table_frame, row)

        # حفظ مرجع إلى إطار الجدول لإضافة صفوف جديدة
        self.bordereau_table_frame = table_frame
        self.bordereau_row_count = 2  # عدد الصفوف الحالي

        # أزرار التحكم في الصفوف
        buttons_frame = ctk.CTkFrame(bordereau_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)

        add_item_btn = ctk.CTkButton(buttons_frame, text="➕ إضافة بند",
                                   command=self.add_bordereau_item,
                                   fg_color="green", hover_color="darkgreen")
        add_item_btn.pack(side="left", padx=5)

        remove_item_btn = ctk.CTkButton(buttons_frame, text="➖ حذف بند",
                                      command=self.remove_bordereau_item,
                                      fg_color="red", hover_color="darkred")
        remove_item_btn.pack(side="left", padx=5)

        # ملخص الأسعار مع الحسابات التلقائية
        self.create_bordereau_summary(bordereau_frame)

    def create_bordereau_summary(self, parent):
        """إنشاء ملخص الأسعار مع الحسابات المطلوبة"""
        summary_frame = ctk.CTkFrame(parent)
        summary_frame.pack(fill="x", padx=10, pady=5)
        summary_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # عنوان قسم الملخص
        summary_title = ctk.CTkLabel(summary_frame, text="Totaux et Calculs",
                                   font=ctk.CTkFont(size=14, weight="bold"))
        summary_title.grid(row=0, column=0, columnspan=4, pady=(5, 10))

        # الصف الأول - TOTAL ACHAT HT و TOTAL VENTE HT
        ctk.CTkLabel(summary_frame, text="TOTAL ACHAT HT:", font=ctk.CTkFont(weight="bold")).grid(row=1, column=0, padx=5, pady=2, sticky="w")
        ctk.CTkEntry(summary_frame, placeholder_text="AUTO").grid(row=1, column=1, padx=5, pady=2, sticky="ew")

        ctk.CTkLabel(summary_frame, text="TOTAL VENTE HT:", font=ctk.CTkFont(weight="bold")).grid(row=1, column=2, padx=5, pady=2, sticky="w")
        ctk.CTkEntry(summary_frame, placeholder_text="AUTO").grid(row=1, column=3, padx=5, pady=2, sticky="ew")

        # الصف الثاني - TVA 20% و TOTAL MARGE HT
        ctk.CTkLabel(summary_frame, text="TVA 20%:", font=ctk.CTkFont(weight="bold")).grid(row=2, column=0, padx=5, pady=2, sticky="w")
        ctk.CTkEntry(summary_frame, placeholder_text="PRIX D'ACHAT HT X 20%").grid(row=2, column=1, padx=5, pady=2, sticky="ew")

        ctk.CTkLabel(summary_frame, text="TOTAL MARGE HT:", font=ctk.CTkFont(weight="bold")).grid(row=2, column=2, padx=5, pady=2, sticky="w")
        ctk.CTkEntry(summary_frame, placeholder_text="VENTE - ACHAT").grid(row=2, column=3, padx=5, pady=2, sticky="ew")

        # الصف الثالث - TTC (مع حقل كبير لعرض النتيجة)
        ctk.CTkLabel(summary_frame, text="TTC:", font=ctk.CTkFont(weight="bold")).grid(row=3, column=0, padx=5, pady=2, sticky="w")
        ttc_entry = ctk.CTkEntry(summary_frame, placeholder_text="HT + TVA", height=40,
                               font=ctk.CTkFont(size=14, weight="bold"))
        ttc_entry.grid(row=3, column=1, columnspan=3, padx=5, pady=2, sticky="ew")

    def create_marche_frais_section(self, parent):
        """إنشاء قسم التكاليف الإضافية"""
        # إطار التكاليف الإضافية
        frais_frame = ctk.CTkFrame(parent)
        frais_frame.pack(fill="x", padx=5, pady=5)

        # عنوان القسم
        section_title = ctk.CTkLabel(frais_frame, text=self.language_manager.get_text("marche_frais"),
                                   font=ctk.CTkFont(size=18, weight="bold"))
        section_title.pack(anchor="w", padx=10, pady=(10, 5))

        # إطار الحقول
        fields_frame = ctk.CTkFrame(frais_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1), weight=1)

        # النقل - مع رقم 500
        self.create_frais_field(fields_frame, "TRANSPORT:", "500 (selectionner mode de paiement)", 0)

        # البريد - مع رقم 501 وملاحظة إضافية
        self.create_frais_field(fields_frame, "Courrier:", "501 (selectionner mode de paiement) si espace ajouter au caisse", 1)

        # التكاليف الأخرى - 7 حقول مع أرقام 502-507
        for i in range(7):
            self.create_frais_field(fields_frame, f"Autres {i+1}:", f"{502+i} (selectionner mode de paiement)", 2+i)

        # الربح المتبقي - حقل أخير في القائمة
        self.create_frais_field(fields_frame, "Reste bénéficiaire:", "AUTO", 9)

    def create_frais_field(self, parent, label_text, default_value, row):
        """إنشاء حقل تكلفة إضافية"""
        # التسمية
        label = ctk.CTkLabel(parent, text=label_text, anchor="w")
        label.grid(row=row, column=0, padx=5, pady=2, sticky="w")

        # الحقل
        entry = ctk.CTkEntry(parent, placeholder_text=default_value)
        entry.grid(row=row, column=1, padx=5, pady=2, sticky="ew")

        return entry

    def create_bordereau_row(self, table_frame, row_number):
        """إنشاء صف في جدول بنود الأسعار حسب المواصفات"""
        # رقم البند (N°) - تسلسل تلقائي
        num_label = ctk.CTkLabel(table_frame, text=str(row_number),
                               font=ctk.CTkFont(weight="bold"))
        num_label.grid(row=row_number, column=0, padx=2, pady=2)

        # الحقول مع القيم الافتراضية حسب المواصفات
        fields_config = [
            # (placeholder_text, is_calculated, calculation_type)
            ("AUTOMATIQUE", False, None),                    # Désignation
            ("automatique", False, None),                    # U (Unité)
            ("AUTO", False, None),                           # Qte
            ("AUTO / MANUEL", False, None),                  # PRIX ACHAT HT
            ("ACHAT x QTE / AUTO / MANUEL", True, "total_achat"),  # TOTAL ACHAT HT
            ("AUTO / MANUEL", False, None),                  # Prix U HT
            ("QTE x Prix U HT", True, "total_vente"),        # TOTAL VENT HT (تصحيح الوصف)
            ("VENT - ACHAT", True, "marge")                  # MARGE
        ]

        # إنشاء حقول الإدخال
        row_entries = {}
        for col, (placeholder, is_calc, calc_type) in enumerate(fields_config, 1):
            entry = ctk.CTkEntry(table_frame, placeholder_text=placeholder)
            entry.grid(row=row_number, column=col, padx=1, pady=2, sticky="ew")

            # حفظ مرجع الحقل للحسابات
            row_entries[col] = {'entry': entry, 'is_calculated': is_calc, 'calc_type': calc_type}

            # إضافة أحداث للحسابات التلقائية
            if col in [3, 4, 6]:  # Qte, PRIX ACHAT HT, Prix U HT
                entry.bind("<KeyRelease>", lambda e, r=row_number: self.calculate_row_totals(r))

        # حفظ معلومات الصف للحسابات
        if not hasattr(self, 'bordereau_rows'):
            self.bordereau_rows = {}
        self.bordereau_rows[row_number] = row_entries

    def add_bordereau_item(self):
        """إضافة بند جديد إلى جدول الأسعار"""
        if not hasattr(self, 'bordereau_table_frame'):
            print("جدول الأسعار غير متاح")
            return

        # زيادة عداد الصفوف
        self.bordereau_row_count += 1
        new_row = self.bordereau_row_count

        # إنشاء الصف الجديد
        self.create_bordereau_row(self.bordereau_table_frame, new_row)

        print(f"✅ تم إضافة بند جديد رقم {new_row}")

    def remove_bordereau_item(self):
        """حذف آخر بند من جدول الأسعار"""
        if not hasattr(self, 'bordereau_table_frame') or self.bordereau_row_count <= 1:
            print("لا يمكن حذف المزيد من البنود")
            return

        # حذف عناصر الصف الأخير
        last_row = self.bordereau_row_count

        # حذف جميع عناصر الصف
        for widget in self.bordereau_table_frame.grid_slaves():
            if int(widget.grid_info()["row"]) == last_row:
                widget.destroy()

        # حذف معلومات الصف
        if hasattr(self, 'bordereau_rows') and last_row in self.bordereau_rows:
            del self.bordereau_rows[last_row]

        self.bordereau_row_count -= 1
        print(f"✅ تم حذف البند رقم {last_row}")

    def calculate_row_totals(self, row_number):
        """حساب المجاميع التلقائية لصف معين حسب المواصفات"""
        try:
            if not hasattr(self, 'bordereau_rows') or row_number not in self.bordereau_rows:
                return

            row_data = self.bordereau_rows[row_number]

            # جلب القيم
            qte_str = row_data[3]['entry'].get()  # Qte
            prix_achat_str = row_data[4]['entry'].get()  # PRIX ACHAT HT
            prix_vente_str = row_data[6]['entry'].get()  # Prix U HT

            # تحويل إلى أرقام
            qte = float(qte_str) if qte_str and qte_str.replace('.', '').isdigit() else 0
            prix_achat = float(prix_achat_str) if prix_achat_str and prix_achat_str.replace('.', '').isdigit() else 0
            prix_vente = float(prix_vente_str) if prix_vente_str and prix_vente_str.replace('.', '').isdigit() else 0

            # حساب TOTAL ACHAT HT = Qte × PRIX ACHAT HT
            total_achat = qte * prix_achat
            row_data[5]['entry'].delete(0, 'end')
            row_data[5]['entry'].insert(0, f"{total_achat:.2f}")

            # حساب TOTAL VENT HT = Qte × Prix U HT
            total_vente = qte * prix_vente
            row_data[7]['entry'].delete(0, 'end')
            row_data[7]['entry'].insert(0, f"{total_vente:.2f}")

            # حساب MARGE = TOTAL VENT HT - TOTAL ACHAT HT
            marge = total_vente - total_achat
            row_data[8]['entry'].delete(0, 'end')
            row_data[8]['entry'].insert(0, f"{marge:.2f}")

            # تحديث المجاميع العامة
            self.calculate_bordereau_totals()

        except Exception as e:
            print(f"❌ خطأ في حساب مجاميع الصف: {e}")

    def calculate_bordereau_totals(self):
        """حساب المجاميع العامة لجدول الأسعار"""
        try:
            if not hasattr(self, 'bordereau_rows'):
                return

            total_achat_ht = 0
            total_vente_ht = 0
            total_marge_ht = 0

            # جمع جميع الصفوف
            for row_num, row_data in self.bordereau_rows.items():
                try:
                    achat = float(row_data[5]['entry'].get() or 0)
                    vente = float(row_data[7]['entry'].get() or 0)
                    marge = float(row_data[8]['entry'].get() or 0)

                    total_achat_ht += achat
                    total_vente_ht += vente
                    total_marge_ht += marge
                except:
                    continue

            # حساب الضريبة (20% من سعر الشراء)
            total_tva = total_achat_ht * 0.20

            # حساب TTC
            total_ttc = total_vente_ht + total_tva

            # تحديث حقل Montant TTC في المعلومات العامة
            if 'montant_ttc' in self.marche_fields:
                self.marche_fields['montant_ttc'].delete(0, 'end')
                self.marche_fields['montant_ttc'].insert(0, f"{total_ttc:.2f}")

                # حساب الضمان النهائي تلقائياً
                self.calculate_caution_definitif()

            print(f"✅ تم حساب المجاميع: ACHAT={total_achat_ht:.2f}, VENTE={total_vente_ht:.2f}, MARGE={total_marge_ht:.2f}, TTC={total_ttc:.2f}")

        except Exception as e:
            print(f"❌ خطأ في حساب المجاميع العامة: {e}")

    def update_bordereau_summary(self):
        """تحديث ملخص الأسعار العام"""
        try:
            if not hasattr(self, 'bordereau_rows'):
                return

            total_achat_global = 0
            total_vente_global = 0

            # حساب المجاميع من جميع الصفوف
            for row_num, row_data in self.bordereau_rows.items():
                try:
                    total_achat_str = row_data[5]['entry'].get()  # TOTAL ACHAT HT
                    total_vente_str = row_data[7]['entry'].get()  # TOTAL VENT HT

                    if total_achat_str and total_achat_str.replace('.', '').isdigit():
                        total_achat_global += float(total_achat_str)
                    if total_vente_str and total_vente_str.replace('.', '').isdigit():
                        total_vente_global += float(total_vente_str)
                except:
                    continue

            # حساب TVA و TTC
            tva_rate = 0.20  # 20%
            tva_amount = total_achat_global * tva_rate
            ttc_amount = total_achat_global + tva_amount
            total_marge = total_vente_global - total_achat_global

            print(f"Résumé des prix: Total achat={self.format_currency(total_achat_global)}, Total vente={self.format_currency(total_vente_global)}, Marge={self.format_currency(total_marge)}")

        except Exception as e:
            print(f"خطأ في تحديث ملخص الأسعار: {e}")

    def show_stock_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text="Gestion du Stock", font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Statistiques du stock
        stats_frame = ctk.CTkFrame(self.main_content_frame)
        stats_frame.pack(fill="x", pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2), weight=1)

        total_products = self.get_products_count()
        low_stock_count = self.get_low_stock_count()
        total_value = self.get_total_stock_value()

        self.create_stat_card(stats_frame, "Total Produits", total_products, "#3b82f6", 0)
        self.create_stat_card(stats_frame, "Stock Faible", low_stock_count, "#ef4444", 1)
        self.create_stat_card(stats_frame, "Valeur Stock", f"{total_value:.2f} DH", "#22c55e", 2)

        # Barre d'outils pour le stock
        stock_toolbar_frame = ctk.CTkFrame(self.main_content_frame)
        stock_toolbar_frame.pack(fill="x", padx=10, pady=5)

        update_stock_btn = ctk.CTkButton(stock_toolbar_frame, text="Mettre à Jour Stock", command=self.update_stock)
        update_stock_btn.pack(side="right", padx=5, pady=5)

        export_stock_btn = ctk.CTkButton(stock_toolbar_frame, text="Exporter Stock", command=self.export_stock)
        export_stock_btn.pack(side="right", padx=5, pady=5)

        inventory_btn = ctk.CTkButton(stock_toolbar_frame, text="Inventaire", command=self.start_inventory)
        inventory_btn.pack(side="right", padx=5, pady=5)

        # Tableau du stock
        self.create_stock_table(self.main_content_frame)

    def show_cash_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text="Gestion de la Caisse", font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Solde de la caisse
        balance_frame = ctk.CTkFrame(self.main_content_frame)
        balance_frame.pack(fill="x", pady=10)

        current_balance = self.get_cash_balance()
        balance_label = ctk.CTkLabel(balance_frame, text=f"Solde Actuel: {self.format_currency(current_balance)}",
                                   font=ctk.CTkFont(size=20, weight="bold"))
        balance_label.pack(pady=20)

        # Boutons d'actions
        actions_frame = ctk.CTkFrame(self.main_content_frame)
        actions_frame.pack(fill="x", pady=5)

        add_entry_btn = ctk.CTkButton(actions_frame, text="Ajouter Entrée", command=self.show_add_cash_entry_dialog)
        add_entry_btn.pack(side="left", padx=10, pady=10)

        add_exit_btn = ctk.CTkButton(actions_frame, text="Ajouter Sortie", command=self.show_add_cash_exit_dialog)
        add_exit_btn.pack(side="left", padx=10, pady=10)

        transfer_btn = ctk.CTkButton(actions_frame, text="Virement", command=self.show_transfer_dialog)
        transfer_btn.pack(side="left", padx=10, pady=10)

        export_cash_btn = ctk.CTkButton(actions_frame, text="Exporter Mouvements", command=self.export_cash_movements)
        export_cash_btn.pack(side="left", padx=10, pady=10)

        # Tableau des mouvements de caisse
        self.create_cash_movements_table(self.main_content_frame)

    def show_tva_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text="Gestion de la TVA", font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Période de calcul
        period_frame = ctk.CTkFrame(self.main_content_frame)
        period_frame.pack(fill="x", pady=10)

        period_label = ctk.CTkLabel(period_frame, text="Période:", font=ctk.CTkFont(size=14, weight="bold"))
        period_label.pack(side="left", padx=10, pady=10)

        month_combo = ctk.CTkComboBox(period_frame, values=["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                                                           "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"])
        month_combo.pack(side="left", padx=5, pady=10)

        year_combo = ctk.CTkComboBox(period_frame, values=["2024", "2023", "2022"])
        year_combo.pack(side="left", padx=5, pady=10)

        calculate_btn = ctk.CTkButton(period_frame, text="Calculer TVA", command=self.calculate_tva)
        calculate_btn.pack(side="left", padx=10, pady=10)

        export_tva_btn = ctk.CTkButton(period_frame, text="Exporter TVA", command=self.export_tva)
        export_tva_btn.pack(side="left", padx=10, pady=10)

        print_tva_btn = ctk.CTkButton(period_frame, text="Imprimer Déclaration", command=self.print_tva_declaration)
        print_tva_btn.pack(side="left", padx=10, pady=10)

        # Résumé TVA
        self.create_tva_summary(self.main_content_frame)

    def show_reports_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text="Rapports et Statistiques", font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Types de rapports
        reports_frame = ctk.CTkFrame(self.main_content_frame)
        reports_frame.pack(fill="both", expand=True, pady=10)

        reports_grid = ctk.CTkFrame(reports_frame)
        reports_grid.pack(expand=True, fill="both", padx=20, pady=20)

        # Boutons de rapports
        reports = [
            ("Rapport des Ventes", self.generate_sales_report),
            ("Rapport des Achats", self.generate_purchases_report),
            ("Rapport de Stock", self.generate_stock_report),
            ("Rapport Financier", self.generate_financial_report),
            ("Rapport TVA", self.generate_tva_report),
            ("Rapport Fournisseurs", self.generate_suppliers_report)
        ]

        for i, (report_name, command) in enumerate(reports):
            row = i // 2
            col = i % 2
            btn = ctk.CTkButton(reports_grid, text=report_name, command=command, width=200, height=60)
            btn.grid(row=row, column=col, padx=20, pady=20)

        # Boutons d'actions pour les rapports
        reports_actions_frame = ctk.CTkFrame(reports_frame)
        reports_actions_frame.pack(fill="x", padx=20, pady=10)

        export_all_btn = ctk.CTkButton(reports_actions_frame, text="Exporter Tous les Rapports", command=self.export_all_reports)
        export_all_btn.pack(side="left", padx=10, pady=5)

        schedule_btn = ctk.CTkButton(reports_actions_frame, text="Programmer Rapports", command=self.schedule_reports)
        schedule_btn.pack(side="left", padx=10, pady=5)

        custom_report_btn = ctk.CTkButton(reports_actions_frame, text="Rapport Personnalisé", command=self.create_custom_report)
        custom_report_btn.pack(side="left", padx=10, pady=5)

    def show_search_page(self):
        self.clear_main_content()
        title_label = ctk.CTkLabel(self.main_content_frame, text="Recherche Avancée", font=ctk.CTkFont(size=24, weight="bold"))
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # Zone de recherche
        search_frame = ctk.CTkFrame(self.main_content_frame)
        search_frame.pack(fill="x", pady=10)

        search_label = ctk.CTkLabel(search_frame, text="Rechercher:", font=ctk.CTkFont(size=14, weight="bold"))
        search_label.pack(anchor="w", padx=10, pady=(10, 5))

        search_entry = ctk.CTkEntry(search_frame, placeholder_text="Tapez votre recherche ici...", width=400)
        search_entry.pack(anchor="w", padx=10, pady=5)

        # Filtres
        filters_frame = ctk.CTkFrame(search_frame)
        filters_frame.pack(fill="x", padx=10, pady=10)

        filter_label = ctk.CTkLabel(filters_frame, text="Rechercher dans:", font=ctk.CTkFont(size=12))
        filter_label.pack(side="left", padx=5)

        category_combo = ctk.CTkComboBox(filters_frame, values=["Tous", "Fournisseurs", "Produits", "Factures"])
        category_combo.pack(side="left", padx=5)

        search_btn = ctk.CTkButton(filters_frame, text="Rechercher", command=self.perform_search)
        search_btn.pack(side="left", padx=10)

        clear_btn = ctk.CTkButton(filters_frame, text="Effacer", command=self.clear_search)
        clear_btn.pack(side="left", padx=5)

        advanced_btn = ctk.CTkButton(filters_frame, text="Recherche Avancée", command=self.show_advanced_search)
        advanced_btn.pack(side="left", padx=5)

        # Résultats de recherche
        results_frame = ctk.CTkFrame(self.main_content_frame)
        results_frame.pack(fill="both", expand=True, pady=10)

        results_label = ctk.CTkLabel(results_frame, text="Résultats de recherche apparaîtront ici", font=ctk.CTkFont(size=14))
        results_label.pack(pady=50)

    # --- وظائف الواجهة الرسومية المساعدة ---
    def create_stat_card(self, parent, title, value, color, column):
        """إنشاء بطاقة إحصائية مع دعم RTL"""
        card = ctk.CTkFrame(parent, border_width=1)
        card.grid(row=0, column=column, padx=10, sticky="ew")

        # تحديد محاذاة النص حسب اللغة
        anchor = self.language_manager.get_anchor_for_language()

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=16),
            anchor=anchor
        )
        title_label.pack(pady=(10, 5), anchor=anchor)

        value_label = ctk.CTkLabel(
            card,
            text=str(value),
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=color,
            anchor=anchor
        )
        value_label.pack(pady=(0, 10), anchor=anchor)

    # --- وظائف قاعدة البيانات ---
    def get_clients_count(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM clients")
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def get_products_count(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM produits")
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def get_invoices_count(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM factures")
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def get_suppliers_count(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def create_clients_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["ID", "Code", "Catégorie", "Client", "ICE", "IF", "Adresse", "Contact", "Date Création"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_client_id = None
        clients_data = self.get_all_clients()

        # حفظ بيانات العملاء لاستخدامها في قائمة السياق
        self.clients_table_data = {}

        for row_idx, client in enumerate(clients_data, start=1):
            # التعامل مع عدد الأعمدة المختلف
            if len(client) >= 12:  # النموذج الجديد
                (id, code, categorie, client_name, ice, if_client, adresse,
                 personne_contacter, contact, n_fix, n_fax, email, date_creation) = client[:13]
            else:  # النموذج القديم
                # معالجة البيانات القديمة
                if len(client) == 8:
                    (id, code, nom, telephone, adresse, ice, if_client, date_creation) = client
                    categorie, client_name, contact = "", nom, telephone
                else:
                    (id, nom, telephone, adresse, ice, if_client, date_creation) = client
                    code, categorie, client_name, contact = "", "", nom, telephone

            # حفظ بيانات العميل
            self.clients_table_data[row_idx] = {
                'id': id,
                'code': code or "",
                'categorie': categorie or "",
                'client': client_name or "",
                'ice': ice or "",
                'if_client': if_client or "",
                'adresse': adresse or "",
                'contact': contact or "",
                'date_creation': date_creation or ""
            }

            display_values = [
                str(id),
                code or "",
                categorie or "",
                client_name or "",
                ice or "",
                if_client or "",
                adresse or "",
                contact or "",
                self.format_date(date_creation)
            ]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_client_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_context_menu)



    def show_client_context_menu(self, event, row):
        """عرض قائمة السياق للعميل"""
        if row not in self.clients_table_data:
            return

        client_data = self.clients_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ {self.language_manager.get_text('edit')} {client_data['nom']}",
            command=lambda: self.edit_client_from_context(client_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ {self.language_manager.get_text('delete')} {client_data['nom']}",
            command=lambda: self.delete_client_from_context(client_data['id'], client_data['nom'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_context_menu(self, event):
        """عرض قائمة سياق عامة"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ {self.language_manager.get_text('add')} {self.language_manager.get_text('clients').rstrip('س')}",
            command=self.show_add_client_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_client_from_context(self, client_id):
        """تعديل عميل من قائمة السياق"""
        self.selected_client_id = client_id
        self.edit_client()

    def delete_client_from_context(self, client_id, client_name):
        """حذف عميل من قائمة السياق"""
        self.selected_client_id = client_id
        self.delete_client()

    def edit_client_from_button(self, client_id):
        """تعديل عميل من زر الإجراء"""
        self.selected_client_id = client_id
        self.edit_client()

    def delete_client_from_button(self, client_id, client_name):
        """حذف عميل من زر الإجراء"""
        self.selected_client_id = client_id
        self.delete_client()

    def show_family_context_menu(self, event, row):
        """عرض قائمة السياق للعائلة"""
        if row not in self.families_table_data:
            return

        family_data = self.families_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ Modifier {family_data['nom']}",
            command=lambda: self.edit_family_from_context(family_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ Supprimer {family_data['nom']}",
            command=lambda: self.delete_family_from_context(family_data['id'], family_data['nom'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()



    def get_all_clients(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM clients ORDER BY date_creation DESC")
        clients = cursor.fetchall()
        conn.close()
        return clients



    def show_add_client_dialog(self):
        if hasattr(self, 'add_client_win') and self.add_client_win.winfo_exists():
            self.add_client_win.focus()
            return

        self.add_client_win = ctk.CTkToplevel(self)
        self.add_client_win.title("Ajouter un Nouveau Client")
        self.add_client_win.geometry("600x700")
        self.add_client_win.transient(self)

        # Frame principal
        frame = ctk.CTkFrame(self.add_client_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        self.client_entries = {}
        row = 0

        # CODE
        label = ctk.CTkLabel(frame, text="CODE")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        code_entry = ctk.CTkEntry(frame, width=200)
        code_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        # Générer le code automatiquement
        auto_code = self.generate_next_client_code()
        code_entry.insert(0, auto_code)
        self.client_entries["code_entry"] = code_entry

        row += 1

        # categorie
        label = ctk.CTkLabel(frame, text="categorie")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        categorie_combo = ctk.CTkComboBox(frame, width=200, values=["publics", "privé"])
        categorie_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["categorie_combo"] = categorie_combo

        row += 1

        # Client
        label = ctk.CTkLabel(frame, text="Client")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        client_entry = ctk.CTkEntry(frame, width=200)
        client_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["client_entry"] = client_entry

        row += 1

        # ICE
        label = ctk.CTkLabel(frame, text="ICE")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        ice_entry = ctk.CTkEntry(frame, width=200)
        ice_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["ice_entry"] = ice_entry

        row += 1

        # IF
        label = ctk.CTkLabel(frame, text="IF")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        if_entry = ctk.CTkEntry(frame, width=200)
        if_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["if_entry"] = if_entry

        row += 1

        # adresse
        label = ctk.CTkLabel(frame, text="adresse")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        adresse_entry = ctk.CTkEntry(frame, width=200)
        adresse_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["adresse_entry"] = adresse_entry

        row += 1

        # personne a contacter
        label = ctk.CTkLabel(frame, text="personne a contacter")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        personne_entry = ctk.CTkEntry(frame, width=200)
        personne_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["personne_entry"] = personne_entry

        row += 1

        # contact
        label = ctk.CTkLabel(frame, text="contact")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        contact_entry = ctk.CTkEntry(frame, width=200)
        contact_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["contact_entry"] = contact_entry

        row += 1

        # N Fix
        label = ctk.CTkLabel(frame, text="N Fix")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        nfix_entry = ctk.CTkEntry(frame, width=200)
        nfix_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["nfix_entry"] = nfix_entry

        row += 1

        # N fax
        label = ctk.CTkLabel(frame, text="N fax")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        nfax_entry = ctk.CTkEntry(frame, width=200)
        nfax_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["nfax_entry"] = nfax_entry

        # 052222225
        note_label10 = ctk.CTkLabel(frame, text="052222225", font=ctk.CTkFont(size=10))
        note_label10.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # adresse electronique
        label = ctk.CTkLabel(frame, text="adresse electronique")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        email_entry = ctk.CTkEntry(frame, width=200)
        email_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.client_entries["email_entry"] = email_entry

        # casablanca
        note_label11 = ctk.CTkLabel(frame, text="casablanca", font=ctk.CTkFont(size=10))
        note_label11.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=row, column=0, columnspan=3, pady=20, sticky="ew")

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler",
                                     command=self.add_client_win.destroy)
        cancel_button.pack(side="left", padx=10, pady=10)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer",
                                   command=self.save_new_client)
        save_button.pack(side="right", padx=10, pady=10)

    def generate_next_client_code(self):
        """Générer le prochain code client basé sur le nombre de clients"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            # Compter tous les clients existants
            cursor.execute("SELECT COUNT(*) FROM clients")
            count = cursor.fetchone()[0]
            conn.close()

            # Le numéro suivant est le nombre de clients + 1
            next_number = count + 1
            return f"C{next_number:03d}"

        except Exception as e:
            print(f"Erreur lors de la génération du code: {e}")
            return "C001"

    def save_new_client(self):
        code = self.client_entries["code_entry"].get()
        client_name = self.client_entries["client_entry"].get()
        if not client_name:
            print("Error: Client name is required")
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO clients (
                code, categorie, client, ice, if_client, adresse,
                personne_contacter, contact, n_fix, n_fax, email
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            code,
            self.client_entries["categorie_combo"].get(),
            client_name,
            self.client_entries["ice_entry"].get(),
            self.client_entries["if_entry"].get(),
            self.client_entries["adresse_entry"].get(),
            self.client_entries["personne_entry"].get(),
            self.client_entries["contact_entry"].get(),
            self.client_entries["nfix_entry"].get(),
            self.client_entries["nfax_entry"].get(),
            self.client_entries["email_entry"].get()
        ))
        conn.commit()
        conn.close()

        print(f"Client '{client_name}' ajouté avec succès!")
        self.add_client_win.destroy()
        self.show_clients_page()  # Refresh the clients page

    def show_edit_client_dialog(self):
        # Utiliser le client sélectionné
        if not hasattr(self, 'selected_client_id') or self.selected_client_id is None:
            print("Aucun client sélectionné")
            return

        # Trouver le client sélectionné
        clients_data = self.get_all_clients()
        client = None
        for c in clients_data:
            if c[0] == self.selected_client_id:
                client = c
                break

        if not client:
            print("Client sélectionné introuvable")
            return

        # التعامل مع عدد الأعمدة المختلف
        if len(client) == 8:  # مع code
            client_id, code, nom, telephone, adresse, ice, if_client, date_creation = client
        else:  # بدون code
            client_id, nom, telephone, adresse, ice, if_client, date_creation = client

        if hasattr(self, 'edit_client_win') and self.edit_client_win.winfo_exists():
            self.edit_client_win.focus()
            return

        self.edit_client_win = ctk.CTkToplevel(self)
        self.edit_client_win.title(f"Modifier Client: {nom}")
        self.edit_client_win.geometry("400x400")
        self.edit_client_win.transient(self)

        frame = ctk.CTkFrame(self.edit_client_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Titre
        title_label = ctk.CTkLabel(frame, text=f"Modification du client: {nom}", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Nom:": "name_entry", "Téléphone:": "phone_entry", "Adresse:": "address_entry",
            "ICE:": "ice_entry", "IF:": "if_entry"
        }

        # Valeurs actuelles
        current_values = {
            "name_entry": nom,
            "phone_entry": telephone or "",
            "address_entry": adresse or "",
            "ice_entry": ice or "",
            "if_entry": if_client or ""
        }

        self.edit_client_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")
            entry = ctk.CTkEntry(frame, width=250)
            entry.insert(0, current_values[name])  # Pré-remplir avec les valeurs actuelles
            entry.grid(row=i, column=1, padx=10, pady=5)
            self.edit_client_entries[name] = entry

        # Stocker l'ID du client pour la mise à jour
        self.current_client_id = client_id

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.update_client)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.edit_client_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def update_client(self):
        name = self.edit_client_entries["name_entry"].get()
        if not name:
            print("Erreur: Le nom est requis")
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE clients
            SET nom = ?, telephone = ?, adresse = ?, ice = ?, if_client = ?
            WHERE id = ?
        """, (
            name,
            self.edit_client_entries["phone_entry"].get(),
            self.edit_client_entries["address_entry"].get(),
            self.edit_client_entries["ice_entry"].get(),
            self.edit_client_entries["if_entry"].get(),
            self.current_client_id
        ))
        conn.commit()
        conn.close()

        print(f"Client {name} mis à jour avec succès!")
        self.edit_client_win.destroy()
        self.show_clients_page()  # Refresh the clients page

    def create_products_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Code", "Désignation", "Prix Achat (DH)", "Prix Vente (DH)", "QTE ACHAT", "STOCK", "Famille", "Fournisseur", "تاريخ الإنشاء"]
        column_widths = [80, 200, 120, 120, 90, 80, 120, 120, 140]  # عرض محسن للأعمدة مع التاريخ

        # إنشاء رؤوس الأعمدة مع تنسيق محسن
        for i, col in enumerate(columns):
            header = ctk.CTkLabel(
                table_frame,
                text=col,
                font=ctk.CTkFont(weight="bold", size=14),
                fg_color=("#3B82F6", "#1E40AF"),  # لون خلفية أزرق
                text_color="white",
                corner_radius=5,
                width=column_widths[i],
                height=35
            )
            header.grid(row=0, column=i, padx=2, pady=5, sticky="ew")

        # تكوين الشبكة لتوزيع الأعمدة بشكل متساوي
        for i in range(len(columns)):
            table_frame.grid_columnconfigure(i, weight=1, minsize=column_widths[i])

        self.selected_product_id = None
        products_data = self.get_all_products()
        # print(f"🔍 تم جلب {len(products_data)} منتج من قاعدة البيانات")  # تعليق للتشخيص

        # حفظ بيانات المنتجات لاستخدامها في قائمة السياق
        self.products_table_data = {}

        for row_idx, product in enumerate(products_data, start=1):
            # استخراج البيانات بالترتيب الجديد
            try:
                # طباعة أول منتج للتشخيص
                # if row_idx == 1:
                #     print(f"🔍 أول منتج في الجدول: {product}")  # تعليق للتشخيص
                
                # الترتيب الجديد: id, code, designation, prix_achat, prix_vente, qte_achat, stock, fournisseur, date_creation, famille_id, famille_nom
                id = product[0]
                code = product[1] or f"P{id:03d}"
                designation = product[2] or "Produit sans nom"
                prix_achat = float(product[3]) if product[3] is not None else 0.0
                prix_vente = float(product[4]) if product[4] is not None else 0.0
                qte_achat = int(product[5]) if product[5] is not None else 0
                stock = int(product[6]) if product[6] is not None else 0
                fournisseur = product[7] if product[7] else ""
                date_creation = product[8] if len(product) > 8 else None
                famille_id = product[9] if len(product) > 9 else None
                famille_nom = product[10] if len(product) > 10 else None

                # print(f"📋 معالجة المنتج {row_idx}: {code} - {designation} - مورد: '{fournisseur}' - عائلة: '{famille_nom}'")  # تعليق للتشخيص

            except (IndexError, ValueError, TypeError) as e:
                print(f"❌ خطأ في معالجة المنتج {row_idx}: {str(e)}")
                print(f"📋 بيانات المنتج: {product}")
                continue

            # حفظ بيانات المنتج مع qte_achat
            self.products_table_data[row_idx] = {
                'id': id,
                'code': code,
                'designation': designation,
                'prix_achat': prix_achat,
                'prix_vente': prix_vente,
                'qte_achat': qte_achat,
                'stock': stock,
                'fournisseur': fournisseur,
                'famille_id': famille_id,
                'famille_nom': famille_nom,
                'date_creation': date_creation
            }

            # تحضير القيم للعرض بالترتيب الصحيح للأعمدة
            # الأعمدة: ["Code", "Désignation", "Prix Achat (DH)", "Prix Vente (DH)", "QTE ACHAT", "STOCK", "Famille", "Fournisseur", "تاريخ الإنشاء"]
            display_values = [
                code,  # 1. Code
                designation,  # 2. Désignation
                self.format_currency(prix_achat) if prix_achat > 0 else self.format_currency(0),  # 3. Prix Achat (DH)
                self.format_currency(prix_vente) if prix_vente > 0 else self.format_currency(0),  # 4. Prix Vente (DH)
                str(qte_achat) if qte_achat > 0 else "0",  # 5. QTE ACHAT
                str(stock) if stock > 0 else "0",  # 6. STOCK
                famille_nom if famille_nom else "Aucune",  # 7. Famille
                fournisseur if fournisseur and fournisseur.strip() else "Aucun",  # 8. Fournisseur
                self.format_date(date_creation)  # 9. تاريخ الإنشاء
            ]
            
            # طباعة أول منتج للتشخيص
            # if row_idx == 1:
            #     print(f"🔍 القيم المعروضة لأول منتج: {display_values}")  # تعليق للتشخيص

            # تحديد لون الصف (تبديل الألوان)
            row_color = ("#F8F9FA", "#E9ECEF") if row_idx % 2 == 0 else ("white", "#F8F9FA")

            for col_idx, value in enumerate(display_values):
                # تنسيق خاص للأعمدة حسب نوعها
                if col_idx in [2, 3]:  # أعمدة الأسعار (Prix Achat, Prix Vente)
                    price_val = prix_achat if col_idx == 2 else prix_vente
                    text_color = ("#28A745", "#20C997") if price_val > 0 else ("#DC3545", "#E74C3C")
                    font_weight = "bold"
                elif col_idx == 4:  # عمود كمية الشراء (QTE ACHAT)
                    text_color = ("#17A2B8", "#138496")  # أزرق للكمية
                    font_weight = "normal"
                elif col_idx == 5:  # عمود المخزون الحالي (STOCK INITIAL)
                    stock_val = stock
                    if stock_val < 5:
                        text_color = ("#DC3545", "#E74C3C")  # أحمر للمخزون المنخفض
                        font_weight = "bold"
                    elif stock_val < 10:
                        text_color = ("#FFC107", "#FFB300")  # أصفر للتحذير
                        font_weight = "normal"
                    else:
                        text_color = ("#28A745", "#20C997")  # أخضر للمخزون الجيد
                        font_weight = "normal"
                elif col_idx == 6:  # عمود العائلة
                    text_color = ("#6F42C1", "#5A2D91")  # بنفسجي للعائلة
                    font_weight = "normal"
                elif col_idx == 7:  # عمود المورد
                    text_color = ("#FD7E14", "#E8590C")  # برتقالي للمورد
                    font_weight = "normal"
                elif col_idx == 8:  # عمود التاريخ
                    text_color = ("#6C757D", "#495057")  # رمادي للتاريخ
                    font_weight = "normal"
                else:  # الأعمدة الأخرى (Code, Designation)
                    text_color = ("#212529", "#495057")
                    font_weight = "normal"

                cell = ctk.CTkLabel(
                    table_frame,
                    text=str(value),
                    anchor="w",
                    fg_color=row_color,
                    text_color=text_color,
                    font=ctk.CTkFont(size=12, weight=font_weight),
                    width=column_widths[col_idx],
                    height=30,
                    corner_radius=3
                )
                cell.grid(row=row_idx, column=col_idx, padx=2, pady=1, sticky="ew")

                # إضافة تأثير التمرير عند المرور بالماوس
                def on_enter(event, cell=cell):
                    cell.configure(fg_color=("#E3F2FD", "#BBDEFB"))

                def on_leave(event, cell=cell, original_color=row_color):
                    cell.configure(fg_color=original_color)

                cell.bind("<Enter>", on_enter)
                cell.bind("<Leave>", on_leave)

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_product_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_products_context_menu)

        # إضافة رسالة إذا لم توجد منتجات
        if not products_data:
            no_data_label = ctk.CTkLabel(
                table_frame,
                text="لا توجد منتجات مضافة بعد",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color="gray"
            )
            no_data_label.grid(row=1, column=0, columnspan=len(columns), pady=50)
        else:
            # إضافة شريط إحصائيات في أسفل الجدول
            stats_row = len(products_data) + 1

            # حساب الإحصائيات
            total_products = len(products_data)
            # حساب المخزون المنخفض (المخزون في الفهرس 5)
            low_stock_count = sum(1 for product in products_data if len(product) > 5 and product[5] is not None and int(product[5]) < 5)
            # حساب قيمة المخزون (سعر الشراء * المخزون)
            total_value = sum(float(product[3]) * int(product[5]) if len(product) > 5 and product[3] and product[5] else 0 for product in products_data)

            # شريط فاصل
            separator = ctk.CTkFrame(table_frame, height=2, fg_color=("#DEE2E6", "#6C757D"))
            separator.grid(row=stats_row, column=0, columnspan=len(columns), sticky="ew", padx=2, pady=5)

            # معلومات إحصائية
            stats_text = f"Total produits: {total_products} | Stock faible: {low_stock_count} | Valeur stock: {self.format_currency(total_value)}"

            stats_label = ctk.CTkLabel(
                table_frame,
                text=stats_text,
                font=ctk.CTkFont(size=12, weight="bold"),
                fg_color=("#F8F9FA", "#343A40"),
                text_color=("#495057", "#ADB5BD"),
                corner_radius=5,
                height=35
            )
            stats_label.grid(row=stats_row + 1, column=0, columnspan=len(columns), sticky="ew", padx=2, pady=5)
        
        # تحديث الجدول لضمان العرض الصحيح
        table_frame.update_idletasks()
        # print("🔄 تم تحديث جدول المنتجات")  # تعليق للتشخيص

    def show_product_context_menu(self, event, row):
        """عرض قائمة السياق للمنتج"""
        if row not in self.products_table_data:
            return

        product_data = self.products_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {product_data['designation']}",
            command=lambda: self.edit_product_from_context(product_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {product_data['designation']}",
            command=lambda: self.delete_product_from_context(product_data['id'], product_data['designation'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_products_context_menu(self, event):
        """عرض قائمة سياق عامة للمنتجات"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة منتج جديد",
            command=self.show_add_product_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_product_from_context(self, product_id):
        """تعديل منتج من قائمة السياق"""
        self.selected_product_id = product_id
        self.edit_product()

    def delete_product_from_context(self, product_id, product_name):
        """حذف منتج من قائمة السياق"""
        self.selected_product_id = product_id
        self.delete_product()

    def edit_product_from_button(self, product_id):
        """تعديل منتج من زر الإجراء"""
        self.selected_product_id = product_id
        self.edit_product()

    def edit_product(self):
        """دالة تعديل المنتج - تستدعي نافذة التعديل"""
        self.show_edit_product_dialog()

    def delete_product_from_button(self, product_id, product_name):
        """حذف منتج من زر الإجراء"""
        self.selected_product_id = product_id
        self.delete_product()

    def get_all_products(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Vérifier si la colonne famille_id existe
        cursor.execute("PRAGMA table_info(produits)")
        columns = [column[1] for column in cursor.fetchall()]
        # print(f"📋 أعمدة جدول المنتجات: {columns}")  # تعليق للتشخيص

        if 'famille_id' in columns:
            # استعلام محدد لضمان ترتيب الأعمدة مع qte_achat
            cursor.execute("""
                SELECT
                    p.id,
                    p.code,
                    p.designation,
                    p.prix_achat,
                    p.prix_vente,
                    COALESCE(p.qte_achat, 0) as qte_achat,
                    p.stock,
                    p.fournisseur,
                    p.date_creation,
                    p.famille_id,
                    f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY p.date_creation DESC
            """)
        else:
            # Si famille_id n'existe pas, utiliser une requête simple
            cursor.execute("""
                SELECT
                    id,
                    code,
                    designation,
                    prix_achat,
                    prix_vente,
                    COALESCE(qte_achat, 0) as qte_achat,
                    stock,
                    fournisseur,
                    date_creation,
                    NULL as famille_id,
                    NULL as famille_nom
                FROM produits
                ORDER BY date_creation DESC
            """)

        products = cursor.fetchall()
        conn.close()
        return products

    def show_add_product_dialog(self):
        if hasattr(self, 'add_product_win') and self.add_product_win.winfo_exists():
            self.add_product_win.focus()
            return

        self.add_product_win = ctk.CTkToplevel(self)
        self.add_product_win.title("Ajouter un Nouveau Produit")
        self.add_product_win.geometry("600x700")
        self.add_product_win.transient(self)

        # Frame principal
        frame = ctk.CTkFrame(self.add_product_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        self.product_entries = {}
        row = 0

        # Famille
        label = ctk.CTkLabel(frame, text="Famille")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        famille_combo = ctk.CTkComboBox(frame, width=200, command=self.on_famille_changed)
        famille_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.load_families_for_product(famille_combo)
        self.product_entries["famille_combo"] = famille_combo

        # Bouton SELECTIONNER OU AJOUTER
        add_famille_btn = ctk.CTkButton(frame, text="SELECTIONNER OU AJOUTER", width=200,
                                      command=lambda: self.show_add_family_dialog(famille_combo))
        add_famille_btn.grid(row=row, column=2, padx=10, pady=5)
        row += 1

        # CODE
        label = ctk.CTkLabel(frame, text="CODE")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        code_entry = ctk.CTkEntry(frame, width=200)
        code_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        # Générer le code automatiquement
        auto_code = self.generate_next_product_code()
        code_entry.insert(0, auto_code)
        self.product_entries["code_entry"] = code_entry

        # Note explicative
        note_label = ctk.CTkLabel(frame, text="",
                                font=ctk.CTkFont(size=10))
        note_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # Produit
        label = ctk.CTkLabel(frame, text="Produit")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        designation_entry = ctk.CTkEntry(frame, width=200)
        designation_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["designation_entry"] = designation_entry
        row += 1

        # DATE ACHAT
        label = ctk.CTkLabel(frame, text="DATE ACHAT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")

        # حقل تاريخ محسن
        date_achat_entry = create_enhanced_date_entry(
            frame,
            "اختر تاريخ الشراء 📅",
            width=200
        )

        date_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["date_achat_entry"] = date_achat_entry

        # AJOUTER MANUEL
        manuel_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        manuel_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # FOURNISSEUR
        label = ctk.CTkLabel(frame, text="FOURNISSEUR")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        fournisseur_combo = ctk.CTkComboBox(frame, width=200)
        fournisseur_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.load_suppliers_for_product(fournisseur_combo)
        self.product_entries["fournisseur_combo"] = fournisseur_combo
        row += 1

        # N facture d'achat
        label = ctk.CTkLabel(frame, text="N facture d'achat")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        facture_achat_entry = ctk.CTkEntry(frame, width=200)
        facture_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["facture_achat_entry"] = facture_achat_entry
        row += 1

        # PRIX D'achat ht
        label = ctk.CTkLabel(frame, text="PRIX D'achat ht")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_achat_entry = ctk.CTkEntry(frame, width=200)
        prix_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        prix_achat_entry.bind("<KeyRelease>", lambda e: self.calculate_prix_achat_ttc())
        self.product_entries["prix_achat_entry"] = prix_achat_entry

        # Valeur exemple
        prix_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        prix_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # TVA (%)
        label = ctk.CTkLabel(frame, text="TVA (%)")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        tva_combo = ctk.CTkComboBox(frame, width=200, state="readonly", command=lambda x: self.calculate_prix_achat_ttc())
        tva_combo.configure(values=["0", "7", "10", "14", "20"])
        tva_combo.set("20")
        tva_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["tva_combo"] = tva_combo

        tva_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        tva_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # PRIX D'achat TTC
        label = ctk.CTkLabel(frame, text="PRIX D'achat TTC")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_achat_ttc_entry = ctk.CTkEntry(frame, width=200)
        prix_achat_ttc_entry.configure(state="disabled")
        prix_achat_ttc_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["prix_achat_ttc_entry"] = prix_achat_ttc_entry

        # Note HT + TVA
        ttc_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        ttc_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # QTE ACHAT STOCK INITIAL (حقل موحد)
        label = ctk.CTkLabel(frame, text="QTE ACHAT STOCK INITIAL")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        qte_achat_entry = ctk.CTkEntry(frame, width=200)
        qte_achat_entry.insert(0, "1")  # قيمة افتراضية
        qte_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["qte_achat_entry"] = qte_achat_entry

        # ملاحظة
        qte_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        qte_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # PRIX DE VENTE HT (حقل مفقود)
        label = ctk.CTkLabel(frame, text="PRIX DE VENTE HT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_vente_entry = ctk.CTkEntry(frame, width=200)
        prix_vente_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["prix_vente_entry"] = prix_vente_entry

        # Valeur exemple
        prix_vente_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        prix_vente_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # MODE DE PAIEMENT
        label = ctk.CTkLabel(frame, text="MODE DE PAIEMENT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        mode_paiement_combo = ctk.CTkComboBox(frame, width=200)
        mode_paiement_combo.configure(values=["chèque", "espèces", "virement", "carte bancaire"])
        mode_paiement_combo.set("carte bancaire")  # قيمة افتراضية
        mode_paiement_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["mode_paiement_combo"] = mode_paiement_combo

        # SELECTIONNER
        select_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        select_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # date de paiement
        label = ctk.CTkLabel(frame, text="تاريخ الدفع / Date de paiement")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        date_paiement_entry = create_enhanced_date_entry(frame, "اختر تاريخ الدفع 📅", width=200, allow_keyboard_input=True)
        date_paiement_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.product_entries["date_paiement_entry"] = date_paiement_entry
        row += 1

        # استخدام qte_achat_entry كحقل المخزون الوحيد
        # لا حاجة لحقل stock منفصل

        # Ajouter un champ stock minimum (caché)
        stock_min_entry = ctk.CTkEntry(frame, width=1)
        stock_min_entry.insert(0, "0")
        stock_min_entry.grid_remove()  # Cacher le champ
        self.product_entries["stock_min_entry"] = stock_min_entry

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=row, column=0, columnspan=3, pady=20, sticky="ew")

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler",
                                     command=self.add_product_win.destroy)
        cancel_button.pack(side="left", padx=10, pady=10)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer",
                                   command=self.save_new_product)
        save_button.pack(side="right", padx=10, pady=10)
        
        # حساب السعر TTC الأولي
        self.calculate_prix_achat_ttc()



    def generate_next_product_code(self, famille_nom=None):
        """جينيرير البروشين كود برودويت بناءً على العائلة المختارة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إذا لم يتم اختيار عائلة، استخدم P
            if not famille_nom or famille_nom == "-- Aucune famille --":
                prefix = "P"
            else:
                # الحصول على الحرف الأول من العائلة
                prefix = self.get_family_prefix(famille_nom)

            # عد المنتجات التي تبدأ بنفس البادئة
            cursor.execute("SELECT COUNT(*) FROM produits WHERE code LIKE ?", (f"{prefix}%",))
            count = cursor.fetchone()[0]
            conn.close()

            # الرقم التالي هو عدد المنتجات بنفس البادئة + 1
            next_number = count + 1
            return f"{prefix}{next_number:03d}"

        except Exception as e:
            print(f"Erreur lors de la génération du code: {e}")
            return "P001"

    def get_family_prefix(self, famille_nom):
        """الحصول على الحرف الأول من اسم العائلة"""
        if not famille_nom or famille_nom == "-- Aucune famille --":
            return "P"

        # إزالة المسافات والحصول على الحرف الأول
        clean_name = famille_nom.strip()
        if clean_name:
            return clean_name[0].upper()
        return "P"

    def on_famille_changed(self, selected_famille):
        """تحديث كود المنتج عند تغيير العائلة"""
        if hasattr(self, 'product_entries') and 'code_entry' in self.product_entries:
            # توليد كود جديد بناءً على العائلة المختارة
            new_code = self.generate_next_product_code(selected_famille)

            # تحديث حقل الكود
            code_entry = self.product_entries['code_entry']
            code_entry.delete(0, 'end')
            code_entry.insert(0, new_code)

    def load_suppliers_for_product(self, combo_widget):
        """Charger la liste des fournisseurs dans le ComboBox"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            suppliers = cursor.fetchall()
            conn.close()

            supplier_names = ["-- Sélectionner un fournisseur --"] + [supplier[0] for supplier in suppliers]
            combo_widget.configure(values=supplier_names)
            combo_widget.set(supplier_names[0])  # Sélectionner le premier élément
        except Exception as e:
            print(f"Erreur lors du chargement des fournisseurs: {e}")
            combo_widget.configure(values=["-- Aucun fournisseur --"])

    def load_families_for_product(self, combo_widget):
        """Charger la liste des familles dans le ComboBox"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT nom FROM familles_produits ORDER BY nom")
            families = cursor.fetchall()
            conn.close()

            family_names = ["-- Aucune famille --"] + [family[0] for family in families]
            combo_widget.configure(values=family_names)
            combo_widget.set(family_names[0])  # Sélectionner le premier élément
        except Exception as e:
            print(f"Erreur lors du chargement des familles: {e}")
            combo_widget.configure(values=["-- Aucune famille --"])

    def show_add_family_dialog(self, famille_combo):
        """Afficher la boîte de dialogue pour ajouter une nouvelle famille"""
        if hasattr(self, 'add_family_win') and self.add_family_win.winfo_exists():
            self.add_family_win.focus()
            return

        self.add_family_win = ctk.CTkToplevel(self)
        self.add_family_win.title("Ajouter une Famille")
        self.add_family_win.geometry("400x300")
        self.add_family_win.transient(self)

        frame = ctk.CTkFrame(self.add_family_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Titre
        title_label = ctk.CTkLabel(frame, text="Nouvelle Famille de Produit",
                                  font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(pady=10)

        # Nom de la famille
        nom_label = ctk.CTkLabel(frame, text="Nom de la famille:")
        nom_label.pack(pady=5)
        nom_entry = ctk.CTkEntry(frame, width=300, placeholder_text="Ex: Électronique, Alimentaire...")
        nom_entry.pack(pady=5)

        # Description
        desc_label = ctk.CTkLabel(frame, text="Description (optionnelle):")
        desc_label.pack(pady=5)
        desc_entry = ctk.CTkTextbox(frame, width=300, height=80)
        desc_entry.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def save_family():
            nom = nom_entry.get().strip()
            if not nom:
                print("Le nom de la famille est obligatoire")
                return

            description = desc_entry.get("1.0", "end-1c").strip()
            if not description:
                description = None

            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("INSERT INTO familles_produits (nom, description) VALUES (?, ?)",
                             (nom, description))
                conn.commit()
                conn.close()

                print(f"Famille '{nom}' ajoutée avec succès!")

                # Recharger la liste des familles
                self.load_families_for_product(famille_combo)

                # Sélectionner la nouvelle famille
                famille_combo.set(nom)

                # تحديث كود المنتج بناءً على العائلة الجديدة
                self.on_famille_changed(nom)

                self.add_family_win.destroy()

            except sqlite3.IntegrityError:
                print(f"Une famille avec le nom '{nom}' existe déjà")
            except Exception as e:
                print(f"Erreur lors de l'ajout de la famille: {e}")

        cancel_btn = ctk.CTkButton(buttons_frame, text="Annuler",
                                  command=self.add_family_win.destroy)
        cancel_btn.pack(side="left", padx=10)

        save_btn = ctk.CTkButton(buttons_frame, text="Ajouter", command=save_family)
        save_btn.pack(side="right", padx=10)





    def update_family(self):
        """تحديث بيانات العائلة"""
        nom = self.edit_family_nom_entry.get().strip()
        if not nom:
            print("Le nom de la famille est obligatoire")
            return

        description = self.edit_family_desc_entry.get("1.0", "end-1c").strip()
        if not description:
            description = None

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE familles_produits
                SET nom = ?, description = ?
                WHERE id = ?
            """, (nom, description, self.selected_family_id))
            conn.commit()
            conn.close()

            print(f"Famille {nom} mise à jour avec succès!")
            self.edit_family_win.destroy()
            self.show_families_page()  # إعادة تحميل صفحة العائلات
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la famille: {e}")

    def delete_family(self):
        """حذف عائلة"""
        if not hasattr(self, 'selected_family_id') or self.selected_family_id is None:
            print("Veuillez d'abord sélectionner une famille")
            return

        # تأكيد الحذف
        family_data = self.get_family_by_id(self.selected_family_id)
        if not family_data:
            print("Données de la famille introuvables")
            return

        family_id, nom, description, date_creation = family_data

        # فحص إذا كانت هناك منتجات تستخدم هذه العائلة
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM produits WHERE famille_id = ?", (self.selected_family_id,))
        products_count = cursor.fetchone()[0]
        conn.close()

        if products_count > 0:
            print(f"Impossible de supprimer la famille '{nom}' car elle contient {products_count} produit(s)")
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM familles_produits WHERE id = ?", (self.selected_family_id,))
            conn.commit()
            conn.close()

            print(f"Famille '{nom}' supprimée avec succès!")
            self.show_families_page()  # إعادة تحميل صفحة العائلات
        except Exception as e:
            print(f"Erreur lors de la suppression de la famille: {e}")

    def get_family_by_id(self, family_id):
        """جلب بيانات عائلة بالمعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, nom, description, date_creation FROM familles_produits WHERE id = ?", (family_id,))
            family = cursor.fetchone()
            conn.close()
            return family
        except Exception as e:
            print(f"خطأ في جلب بيانات العائلة: {e}")
            return None

    def show_add_family_dialog_standalone(self):
        """عرض نافذة إضافة عائلة جديدة مستقلة"""
        if hasattr(self, 'add_family_standalone_win') and self.add_family_standalone_win.winfo_exists():
            self.add_family_standalone_win.focus()
            return

        self.add_family_standalone_win = ctk.CTkToplevel(self)
        self.add_family_standalone_win.title("Ajouter une nouvelle famille")
        self.add_family_standalone_win.geometry("400x300")
        self.add_family_standalone_win.transient(self)

        frame = ctk.CTkFrame(self.add_family_standalone_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Titre
        title_label = ctk.CTkLabel(frame, text="Ajouter une nouvelle famille", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Nom
        nom_label = ctk.CTkLabel(frame, text="Nom:")
        nom_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.add_family_standalone_nom_entry = ctk.CTkEntry(frame, width=200)
        self.add_family_standalone_nom_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # Description
        desc_label = ctk.CTkLabel(frame, text="Description:")
        desc_label.grid(row=2, column=0, padx=10, pady=5, sticky="nw")
        self.add_family_standalone_desc_entry = ctk.CTkTextbox(frame, width=200, height=80)
        self.add_family_standalone_desc_entry.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        cancel_btn = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_family_standalone_win.destroy)
        cancel_btn.pack(side="left", padx=5)

        save_btn = ctk.CTkButton(buttons_frame, text="Sauvegarder", command=self.save_new_family_standalone)
        save_btn.pack(side="left", padx=5)

    def save_new_family_standalone(self):
        """حفظ عائلة جديدة من النافذة المستقلة"""
        nom = self.add_family_standalone_nom_entry.get().strip()
        if not nom:
            print("Le nom de la famille est obligatoire")
            return

        description = self.add_family_standalone_desc_entry.get("1.0", "end-1c").strip()
        if not description:
            description = None

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("INSERT INTO familles_produits (nom, description) VALUES (?, ?)",
                         (nom, description))
            conn.commit()
            conn.close()

            print(f"Famille '{nom}' ajoutée avec succès!")

            # إغلاق النافذة وتحديث العرض
            self.add_family_standalone_win.destroy()
            self.show_families_page()  # تحديث صفحة العائلات

        except Exception as e:
            print(f"Erreur lors de l'ajout de la famille: {e}")

    def save_new_product(self):
        code = self.product_entries["code_entry"].get()
        designation = self.product_entries["designation_entry"].get()

        if not code or not designation:
            print("Erreur: Le code et la désignation sont obligatoires")
            return

        # Récupérer les valeurs des champs
        fournisseur = self.product_entries["fournisseur_combo"].get()
        if fournisseur == "-- Sélectionner un fournisseur --":
            fournisseur = None

        # Nouveaux champs
        date_achat = self.parse_date_input(self.product_entries["date_achat_entry"].get())
        facture_achat = self.product_entries["facture_achat_entry"].get()
        stock_min = self.product_entries["stock_min_entry"].get()
        mode_paiement = self.product_entries["mode_paiement_combo"].get()
        date_paiement = self.parse_date_input(self.product_entries["date_paiement_entry"].get())
        tva_text = self.product_entries["tva_combo"].get()
        tva_rate = float(tva_text.replace('%', '')) if tva_text else 20

        famille_nom = self.product_entries["famille_combo"].get()
        famille_id = None
        if famille_nom and famille_nom != "-- Aucune famille --":
            # Récupérer l'ID de la famille
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM familles_produits WHERE nom = ?", (famille_nom,))
                result = cursor.fetchone()
                if result:
                    famille_id = result[0]
                conn.close()
            except Exception as e:
                print(f"Erreur lors de la récupération de la famille: {e}")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO produits (
                    code, designation, prix_achat, prix_vente, qte_achat, stock, stock_minimum,
                    fournisseur, famille_id, date_achat, facture_achat,
                    tva_rate, mode_paiement, date_paiement
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                code,
                designation,
                float(self.product_entries["prix_achat_entry"].get() or 0),
                float(self.product_entries["prix_vente_entry"].get() or 0),
                int(self.product_entries["qte_achat_entry"].get() or 1),
                int(self.product_entries["qte_achat_entry"].get() or 1),  # استخدام نفس القيمة للمخزون
                int(stock_min or 0),
                fournisseur,
                famille_id,
                date_achat,
                facture_achat,
                tva_rate,
                mode_paiement,
                date_paiement
            ))
            conn.commit()
            print(f"Produit '{designation}' ajouté avec succès!")

            # Afficher un message de succès
            import tkinter.messagebox as messagebox
            messagebox.showinfo("Succès", f"Le produit '{designation}' a été ajouté avec succès!")

            # Fermer la fenêtre et actualiser la liste
            self.add_product_win.destroy()

            # Forcer l'actualisation de la liste des produits
            print("🔄 Actualisation de la liste des produits...")
            self.show_products_page()  # Actualiser la liste des produits
            print("✅ Liste des produits actualisée!")

        except sqlite3.Error as e:
            print(f"Erreur de base de données: {e}")
        finally:
            if conn:
                conn.close()

    def show_edit_product_dialog(self):
        # Utiliser le produit sélectionné
        if not hasattr(self, 'selected_product_id') or self.selected_product_id is None:
            print("Aucun produit sélectionné")
            return

        # جلب بيانات المنتج من قاعدة البيانات مباشرة للحصول على جميع الحقول
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب جميع بيانات المنتج مع جميع الحقول المتاحة
            cursor.execute("""
                SELECT p.*, f.nom as famille_nom 
                FROM produits p 
                LEFT JOIN familles_produits f ON p.famille_id = f.id 
                WHERE p.id = ?
            """, (self.selected_product_id,))
            
            product_data = cursor.fetchone()
            conn.close()
            
            if not product_data:
                print("Produit sélectionné introuvable")
                return
                
        except Exception as e:
            print(f"Erreur lors de la récupération des données du produit: {e}")
            return

        # استخراج البيانات بأمان
        product_id = product_data[0] if len(product_data) > 0 else None
        code = product_data[1] if len(product_data) > 1 else ""
        designation = product_data[2] if len(product_data) > 2 else ""
        prix_achat = product_data[3] if len(product_data) > 3 else 0
        prix_vente = product_data[4] if len(product_data) > 4 else 0
        qte_achat = product_data[5] if len(product_data) > 5 else 1
        stock = product_data[6] if len(product_data) > 6 else 0
        fournisseur = product_data[7] if len(product_data) > 7 else ""
        famille_id = product_data[8] if len(product_data) > 8 else None
        
        # محاولة جلب الحقول الإضافية إذا كانت موجودة
        date_achat = ""
        facture_achat = ""
        tva_rate = "20"
        mode_paiement = "carte bancaire"
        date_paiement = ""
        
        try:
            # فحص الأعمدة المتاحة في الجدول
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(produits)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            # جلب البيانات الإضافية إذا كانت الأعمدة موجودة
            additional_fields = []
            if 'date_achat' in column_names:
                additional_fields.append('date_achat')
            if 'facture_achat' in column_names:
                additional_fields.append('facture_achat')
            if 'tva_rate' in column_names:
                additional_fields.append('tva_rate')
            if 'mode_paiement' in column_names:
                additional_fields.append('mode_paiement')
            if 'date_paiement' in column_names:
                additional_fields.append('date_paiement')
                
            if additional_fields:
                query = f"SELECT {', '.join(additional_fields)} FROM produits WHERE id = ?"
                cursor.execute(query, (self.selected_product_id,))
                additional_data = cursor.fetchone()
                
                if additional_data:
                    field_index = 0
                    if 'date_achat' in additional_fields:
                        date_achat = additional_data[field_index] or ""
                        field_index += 1
                    if 'facture_achat' in additional_fields:
                        facture_achat = additional_data[field_index] or ""
                        field_index += 1
                    if 'tva_rate' in additional_fields:
                        tva_rate = str(additional_data[field_index]) if additional_data[field_index] else "20"
                        field_index += 1
                    if 'mode_paiement' in additional_fields:
                        mode_paiement = additional_data[field_index] or "carte bancaire"
                        field_index += 1
                    if 'date_paiement' in additional_fields:
                        date_paiement = additional_data[field_index] or ""
                        field_index += 1
            
            conn.close()
        except Exception as e:
            print(f"Erreur lors de la récupération des champs additionnels: {e}")

        # اسم العائلة
        famille_nom = product_data[-1] if len(product_data) > 9 and product_data[-1] else "-- Aucune famille --"

        if hasattr(self, 'edit_product_win') and self.edit_product_win.winfo_exists():
            self.edit_product_win.focus()
            return

        self.edit_product_win = ctk.CTkToplevel(self)
        self.edit_product_win.title(f"Modifier Produit: {designation}")
        self.edit_product_win.geometry("600x900")
        self.edit_product_win.transient(self)

        # إطار قابل للتمرير
        scrollable_frame = ctk.CTkScrollableFrame(self.edit_product_win)
        scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = ctk.CTkLabel(scrollable_frame, text=f"Modification du produit: {designation}", font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=(0, 20))

        # إعداد قاموس الحقول للتعديل
        self.edit_product_entries = {}

        # إنشاء نفس الحقول كما في نافذة الإضافة مع جميع القيم الحالية
        try:
            self.create_edit_product_fields(scrollable_frame, current_values={
                "famille": famille_nom,
                "code": code,
                "designation": designation,
                "date_achat": date_achat,
                "fournisseur": fournisseur,
                "facture_achat": facture_achat,
                "prix_achat": str(prix_achat) if prix_achat else "0",
                "tva_rate": tva_rate,
                "qte_achat": str(qte_achat) if qte_achat else "1",
                "prix_vente": str(prix_vente) if prix_vente else "0",
                "mode_paiement": mode_paiement,
                "date_paiement": date_paiement
            })
        except Exception as e:
            print(f"Erreur lors de la création des champs: {e}")
            # عرض رسالة خطأ بسيطة
            error_label = ctk.CTkLabel(scrollable_frame, text=f"Erreur: {e}", text_color="red")
            error_label.pack(pady=20)

        # حفظ ID المنتج
        self.current_product_id = product_id

        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(scrollable_frame)
        buttons_frame.pack(pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.update_product, width=120)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.edit_product_win.destroy, width=120)
        cancel_button.pack(side="left", padx=10)

    def create_edit_product_fields(self, parent, current_values):
        """إنشاء جميع حقول تعديل المنتج بنفس شكل نافذة الإضافة مع ملء القيم الحالية"""
        frame = ctk.CTkFrame(parent)
        frame.pack(fill="x", padx=10, pady=10)
        frame.grid_columnconfigure(1, weight=1)

        row = 0

        # Famille
        label = ctk.CTkLabel(frame, text="Famille")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        famille_combo = ctk.CTkComboBox(frame, width=200, command=self.on_famille_changed)
        self.load_families_for_product(famille_combo)
        famille_combo.set(current_values.get("famille", "-- Aucune famille --"))
        famille_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["famille_combo"] = famille_combo

        add_famille_btn = ctk.CTkButton(frame, text="SELECTIONNER OU AJOUTER", width=200,
                                        command=lambda: self.show_add_family_dialog(famille_combo))
        add_famille_btn.grid(row=row, column=2, padx=10, pady=5)
        row += 1

        # CODE
        label = ctk.CTkLabel(frame, text="CODE")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        code_entry = ctk.CTkEntry(frame, width=200)
        code_entry.insert(0, current_values.get("code", ""))
        code_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["code_entry"] = code_entry
        note_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        note_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # Produit
        label = ctk.CTkLabel(frame, text="Produit")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        designation_entry = ctk.CTkEntry(frame, width=200)
        designation_entry.insert(0, current_values.get("designation", ""))
        designation_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["designation_entry"] = designation_entry
        row += 1

        # DATE ACHAT
        label = ctk.CTkLabel(frame, text="DATE ACHAT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        date_achat_entry = create_enhanced_date_entry(
            frame,
            "اختر تاريخ الشراء 📅",
            width=200
        )
        # ملء التاريخ الحالي إذا كان موجوداً
        if current_values.get("date_achat"):
            try:
                # محاولة تعيين التاريخ الحالي
                if hasattr(date_achat_entry, 'delete') and hasattr(date_achat_entry, 'insert'):
                    date_achat_entry.delete(0, 'end')
                    date_achat_entry.insert(0, current_values.get("date_achat"))
            except:
                pass
        date_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["date_achat_entry"] = date_achat_entry
        manuel_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        manuel_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # FOURNISSEUR
        label = ctk.CTkLabel(frame, text="FOURNISSEUR")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        fournisseur_combo = ctk.CTkComboBox(frame, width=200)
        self.load_suppliers_for_product(fournisseur_combo)
        fournisseur_value = current_values.get("fournisseur", "")
        if fournisseur_value:
            fournisseur_combo.set(fournisseur_value)
        else:
            fournisseur_combo.set("-- Sélectionner un fournisseur --")
        fournisseur_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["fournisseur_combo"] = fournisseur_combo
        row += 1

        # N facture d'achat
        label = ctk.CTkLabel(frame, text="N facture d'achat")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        facture_achat_entry = ctk.CTkEntry(frame, width=200)
        facture_achat_entry.insert(0, current_values.get("facture_achat", ""))
        facture_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["facture_achat_entry"] = facture_achat_entry
        row += 1

        # PRIX D'achat ht
        label = ctk.CTkLabel(frame, text="PRIX D'achat ht")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_achat_entry = ctk.CTkEntry(frame, width=200)
        prix_achat_entry.insert(0, current_values.get("prix_achat", "0"))
        prix_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        prix_achat_entry.bind("<KeyRelease>", lambda e: self.calculate_prix_achat_ttc_edit())
        self.edit_product_entries["prix_achat_entry"] = prix_achat_entry
        prix_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        prix_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # TVA (%)
        label = ctk.CTkLabel(frame, text="TVA (%)")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        tva_combo = ctk.CTkComboBox(frame, width=200, state="readonly", command=lambda x: self.calculate_prix_achat_ttc_edit())
        tva_combo.configure(values=["0", "7", "10", "14", "20"])
        tva_combo.set(current_values.get("tva_rate", "20"))
        tva_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["tva_combo"] = tva_combo
        tva_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        tva_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # PRIX D'achat TTC
        label = ctk.CTkLabel(frame, text="PRIX D'achat TTC")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_achat_ttc_entry = ctk.CTkEntry(frame, width=200)
        prix_achat_ttc_entry.configure(state="disabled")
        prix_achat_ttc_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["prix_achat_ttc_entry"] = prix_achat_ttc_entry
        ttc_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        ttc_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # QTE ACHAT STOCK INITIAL
        label = ctk.CTkLabel(frame, text="QTE ACHAT STOCK INITIAL")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        qte_achat_entry = ctk.CTkEntry(frame, width=200)
        qte_achat_entry.insert(0, current_values.get("qte_achat", "1"))
        qte_achat_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["qte_achat_entry"] = qte_achat_entry
        qte_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        qte_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # PRIX DE VENTE HT
        label = ctk.CTkLabel(frame, text="PRIX DE VENTE HT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        prix_vente_entry = ctk.CTkEntry(frame, width=200)
        prix_vente_entry.insert(0, current_values.get("prix_vente", "0"))
        prix_vente_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["prix_vente_entry"] = prix_vente_entry
        prix_vente_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        prix_vente_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # MODE DE PAIEMENT
        label = ctk.CTkLabel(frame, text="MODE DE PAIEMENT")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        mode_paiement_combo = ctk.CTkComboBox(frame, width=200)
        mode_paiement_combo.configure(values=["chèque", "espèces", "virement", "carte bancaire"])
        mode_paiement_combo.set(current_values.get("mode_paiement", "carte bancaire"))
        mode_paiement_combo.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["mode_paiement_combo"] = mode_paiement_combo
        select_label = ctk.CTkLabel(frame, text="", font=ctk.CTkFont(size=10))
        select_label.grid(row=row, column=2, padx=10, pady=5, sticky="w")
        row += 1

        # date de paiement
        label = ctk.CTkLabel(frame, text="تاريخ الدفع / Date de paiement")
        label.grid(row=row, column=0, padx=10, pady=5, sticky="w")
        date_paiement_entry = create_enhanced_date_entry(frame, "اختر تاريخ الدفع 📅", width=200, allow_keyboard_input=True)
        # تنسيق التاريخ للإدخال
        formatted_date = self.format_date_for_input(current_values.get("date_paiement", ""))
        date_paiement_entry.delete(0, ctk.END)
        date_paiement_entry.insert(0, formatted_date)
        date_paiement_entry.grid(row=row, column=1, padx=10, pady=5, sticky="w")
        self.edit_product_entries["date_paiement_entry"] = date_paiement_entry
        row += 1

        # Champ stock minimum (caché)
        stock_min_entry = ctk.CTkEntry(frame, width=1)
        stock_min_entry.insert(0, "0")
        stock_min_entry.grid_remove()
        self.edit_product_entries["stock_min_entry"] = stock_min_entry
        
        # حساب السعر شامل الضريبة عند التحميل
        self.calculate_prix_achat_ttc_edit()

    def calculate_prix_achat_ttc_edit(self):
        """حساب السعر شامل الضريبة في نافذة التعديل"""
        try:
            if not hasattr(self, 'edit_product_entries'):
                return
                
            prix_achat_entry = self.edit_product_entries.get("prix_achat_entry")
            tva_combo = self.edit_product_entries.get("tva_combo")
            prix_achat_ttc_entry = self.edit_product_entries.get("prix_achat_ttc_entry")
            
            if not all([prix_achat_entry, tva_combo, prix_achat_ttc_entry]):
                return
                
            # الحصول على القيم
            prix_ht = float(prix_achat_entry.get() or 0)
            tva_rate = float(tva_combo.get() or 20)
            
            # حساب السعر شامل الضريبة
            prix_ttc = prix_ht * (1 + tva_rate / 100)
            
            # تحديث الحقل
            prix_achat_ttc_entry.configure(state="normal")
            prix_achat_ttc_entry.delete(0, 'end')
            prix_achat_ttc_entry.insert(0, f"{prix_ttc:.2f}")
            prix_achat_ttc_entry.configure(state="disabled")
            
        except (ValueError, AttributeError):
            pass

    def update_product(self):
        """تحديث بيانات المنتج في قاعدة البيانات"""
        try:
            if not hasattr(self, 'edit_product_entries') or not hasattr(self, 'current_product_id'):
                print("❌ خطأ: بيانات التعديل غير متاحة")
                return

            # جمع البيانات من الحقول
            code = self.edit_product_entries["code_entry"].get().strip()
            designation = self.edit_product_entries["designation_entry"].get().strip()
            
            if not code or not designation:
                print("❌ خطأ: الكود والتسمية مطلوبان")
                import tkinter.messagebox as messagebox
                messagebox.showerror("خطأ", "الكود والتسمية مطلوبان")
                return

            # جمع باقي البيانات
            prix_achat = float(self.edit_product_entries["prix_achat_entry"].get() or 0)
            prix_vente = float(self.edit_product_entries["prix_vente_entry"].get() or 0)
            qte_achat = int(self.edit_product_entries["qte_achat_entry"].get() or 1)
            
            # الحصول على المورد
            fournisseur = self.edit_product_entries["fournisseur_combo"].get()
            if fournisseur == "-- Sélectionner un fournisseur --":
                fournisseur = ""
            
            # الحصول على العائلة
            famille_nom = self.edit_product_entries["famille_combo"].get()
            famille_id = None
            if famille_nom and famille_nom != "-- Aucune famille --":
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM familles_produits WHERE nom = ?", (famille_nom,))
                result = cursor.fetchone()
                if result:
                    famille_id = result[0]
                conn.close()

            # الحصول على الحقول الإضافية
            date_achat = self.parse_date_input(self.edit_product_entries["date_achat_entry"].get())

            facture_achat = self.edit_product_entries["facture_achat_entry"].get().strip()
            tva_rate = float(self.edit_product_entries["tva_combo"].get() or 20)
            mode_paiement = self.edit_product_entries["mode_paiement_combo"].get()
            date_paiement = self.parse_date_input(self.edit_product_entries["date_paiement_entry"].get())

            # تحديث قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # فحص الأعمدة المتاحة أولاً
            cursor.execute("PRAGMA table_info(produits)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]

            # بناء استعلام التحديث بناءً على الأعمدة المتاحة
            update_fields = [
                "code = ?", "designation = ?", "prix_achat = ?", 
                "prix_vente = ?", "stock = ?", "fournisseur = ?", "famille_id = ?"
            ]
            update_values = [code, designation, prix_achat, prix_vente, qte_achat, fournisseur, famille_id]

            # إضافة الحقول الإضافية إذا كانت موجودة
            if 'qte_achat' in column_names:
                update_fields.append("qte_achat = ?")
                update_values.append(qte_achat)
            
            if 'date_achat' in column_names:
                update_fields.append("date_achat = ?")
                update_values.append(date_achat)
            
            if 'facture_achat' in column_names:
                update_fields.append("facture_achat = ?")
                update_values.append(facture_achat)
            
            if 'tva_rate' in column_names:
                update_fields.append("tva_rate = ?")
                update_values.append(tva_rate)
            
            if 'mode_paiement' in column_names:
                update_fields.append("mode_paiement = ?")
                update_values.append(mode_paiement)
            
            if 'date_paiement' in column_names:
                update_fields.append("date_paiement = ?")
                update_values.append(date_paiement)
            
            # حساب وإضافة الأسعار شاملة الضريبة
            prix_achat_ttc = prix_achat * (1 + tva_rate / 100)
            prix_vente_ttc = prix_vente * (1 + tva_rate / 100)
            
            if 'prix_achat_ttc' in column_names:
                update_fields.append("prix_achat_ttc = ?")
                update_values.append(prix_achat_ttc)
            
            if 'prix_vente_ttc' in column_names:
                update_fields.append("prix_vente_ttc = ?")
                update_values.append(prix_vente_ttc)

            # إضافة ID المنتج في النهاية
            update_values.append(self.current_product_id)

            # تنفيذ الاستعلام
            query = f"UPDATE produits SET {', '.join(update_fields)} WHERE id = ?"
            print(f"🔧 تنفيذ الاستعلام: {query}")
            print(f"🔧 القيم: {update_values}")
            
            cursor.execute(query, update_values)
            
            # التحقق من أن التحديث تم بنجاح
            if cursor.rowcount > 0:
                conn.commit()
                print(f"✅ تم تحديث المنتج '{designation}' بنجاح - تم تحديث {cursor.rowcount} صف")
                
                # التحقق من البيانات المحدثة
                cursor.execute("SELECT code, designation, prix_achat, prix_vente, stock FROM produits WHERE id = ?", (self.current_product_id,))
                updated_data = cursor.fetchone()
                if updated_data:
                    print(f"🔍 البيانات المحدثة: {updated_data}")
                else:
                    print("⚠️ لم يتم العثور على البيانات المحدثة")
                
                # إظهار رسالة نجاح
                import tkinter.messagebox as messagebox
                messagebox.showinfo("نجح", f"تم تحديث المنتج '{designation}' بنجاح!")

                # إغلاق نافذة التعديل
                self.edit_product_win.destroy()

                # تحديث قائمة المنتجات
                print("🔄 تحديث عرض المنتجات...")
                
                # مسح cache البيانات
                if hasattr(self, 'products_table_data'):
                    self.products_table_data.clear()
                
                # تحديث قسري للواجهة
                self.update_idletasks()
                self.after(100, lambda: self.show_products_page())  # تأخير قصير لضمان التحديث
                
                print("✅ تم تحديث عرض المنتجات")
                
            else:
                print(f"⚠️ لم يتم العثور على المنتج بالمعرف {self.current_product_id}")
                conn.rollback()
                
                # إظهار رسالة خطأ
                import tkinter.messagebox as messagebox
                messagebox.showerror("خطأ", f"لم يتم العثور على المنتج للتحديث!")
            
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحديث المنتج: {e}")
            import traceback
            traceback.print_exc()
            
            # إغلاق الاتصال في حالة الخطأ
            try:
                if 'conn' in locals():
                    conn.rollback()
                    conn.close()
            except:
                pass
            
            import tkinter.messagebox as messagebox
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث المنتج:\n{e}")

    def get_recent_invoices(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT f.id, f.numero, c.nom, f.date_facture, f.montant_ttc, f.statut
            FROM factures f
            LEFT JOIN clients c ON f.client_id = c.id
            ORDER BY f.date_facture DESC
            LIMIT 10
        """)
        invoices = cursor.fetchall()
        conn.close()
        return invoices

    def add_sample_data(self):
        """إضافة بيانات تجريبية إذا لم تكن موجودة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # فحص إذا كانت هناك بيانات موجودة
        cursor.execute("SELECT COUNT(*) FROM clients")
        clients_count = cursor.fetchone()[0]

        if clients_count == 0:
            # إضافة عملاء تجريبيين
            sample_clients = [
                ("شركة النور", "0612345678", "الدار البيضاء", "ICE001", "IF001"),
                ("محمد أحمد", "0623456789", "الرباط", "ICE002", "IF002"),
                ("فاطمة الزهراء", "0634567890", "فاس", "ICE003", "IF003")
            ]

            for client in sample_clients:
                cursor.execute("""
                    INSERT INTO clients (nom, telephone, adresse, ice, if_client)
                    VALUES (?, ?, ?, ?, ?)
                """, client)

            # إضافة موردين تجريبيين
            sample_suppliers = [
                ("شركة التقنية", "0512345678", "الدار البيضاء", "ICE101", "IF101"),
                ("شركة العرض", "0523456789", "الرباط", "ICE102", "IF102"),
                ("مورد المكتب", "0534567890", "فاس", "ICE103", "IF103")
            ]

            for supplier in sample_suppliers:
                cursor.execute("""
                    INSERT INTO fournisseurs (nom, telephone, adresse, ice, if_fournisseur)
                    VALUES (?, ?, ?, ?, ?)
                """, supplier)

            # إضافة منتجات تجريبية
            sample_products = [
                ("P001", "لاب توب HP", 5000.0, 6000.0, 10, "شركة التقنية"),
                ("P002", "ماوس لوجيتيك", 150.0, 200.0, 25, "شركة التقنية"),
                ("P003", "لوحة مفاتيح", 200.0, 250.0, 15, "شركة التقنية"),
                ("P004", "شاشة 24 بوصة", 1500.0, 1800.0, 8, "شركة العرض")
            ]

            for product in sample_products:
                try:
                    cursor.execute("""
                        INSERT INTO produits (code, designation, prix_achat, prix_vente, stock, fournisseur)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, product)
                except sqlite3.IntegrityError:
                    # المنتج موجود مسبقاً
                    pass

            # إضافة فواتير تجريبية
            sample_invoices = [
                ("FAC001", 1, "2024-01-15", 6000.0, 1200.0, 7200.0, "مدفوعة"),
                ("FAC002", 2, "2024-01-16", 450.0, 90.0, 540.0, "في الانتظار"),
                ("FAC003", 3, "2024-01-17", 1800.0, 360.0, 2160.0, "مدفوعة")
            ]

            for invoice in sample_invoices:
                cursor.execute("""
                    INSERT OR IGNORE INTO factures (numero, client_id, date_facture, montant_ht, montant_tva, montant_ttc, statut)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, invoice)

            conn.commit()
            print("✨ تم إضافة بيانات تجريبية")

        conn.close()

    def create_suppliers_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Code", "Nom", "Téléphone", "Adresse", "ICE", "IF", "Date Création"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_supplier_id = None
        suppliers_data = self.get_all_suppliers()

        # حفظ بيانات الموردين لاستخدامها في قائمة السياق
        self.suppliers_table_data = {}

        for row_idx, supplier in enumerate(suppliers_data, start=1):
            # التعامل مع البيانات الجديدة مع عمود الكود
            if len(supplier) >= 8:
                (id, code, nom, telephone, adresse, ice, if_fournisseur, date_creation) = supplier[:8]
            else:
                # إذا كان هناك أعمدة أقل، استخدم قيم افتراضية
                id = supplier[0] if len(supplier) > 0 else None
                code = supplier[1] if len(supplier) > 1 else ""
                nom = supplier[2] if len(supplier) > 2 else ""
                telephone = supplier[3] if len(supplier) > 3 else ""
                adresse = supplier[4] if len(supplier) > 4 else ""
                ice = supplier[5] if len(supplier) > 5 else ""
                if_fournisseur = supplier[6] if len(supplier) > 6 else ""
                date_creation = supplier[7] if len(supplier) > 7 else ""

            # حفظ بيانات المورد
            self.suppliers_table_data[row_idx] = {
                'id': id,
                'code': code,
                'nom': nom,
                'telephone': telephone,
                'adresse': adresse,
                'ice': ice,
                'if_fournisseur': if_fournisseur,
                'date_creation': date_creation
            }

            display_values = [
                code or "",
                nom,
                telephone or "",
                adresse or "",
                ice or "",
                if_fournisseur or "",
                self.format_date(date_creation)
            ]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_supplier_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_suppliers_context_menu)

    def show_supplier_context_menu(self, event, row):
        """عرض قائمة السياق للمورد"""
        if row not in self.suppliers_table_data:
            return

        supplier_data = self.suppliers_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {supplier_data['nom']}",
            command=lambda: self.edit_supplier_from_context(supplier_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {supplier_data['nom']}",
            command=lambda: self.delete_supplier_from_context(supplier_data['id'], supplier_data['nom'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_suppliers_context_menu(self, event):
        """عرض قائمة سياق عامة للموردين"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة مورد جديد",
            command=self.show_add_supplier_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_supplier_from_context(self, supplier_id):
        """تعديل مورد من قائمة السياق"""
        self.selected_supplier_id = supplier_id
        self.edit_supplier()

    def delete_supplier_from_context(self, supplier_id, supplier_name):
        """حذف مورد من قائمة السياق"""
        self.selected_supplier_id = supplier_id
        self.delete_supplier()

    def edit_supplier_from_button(self, supplier_id):
        """تعديل مورد من زر الإجراء"""
        self.selected_supplier_id = supplier_id
        self.edit_supplier()

    def delete_supplier_from_button(self, supplier_id, supplier_name):
        """حذف مورد من زر الإجراء"""
        self.selected_supplier_id = supplier_id
        self.delete_supplier()

    def get_all_suppliers(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, code, nom, telephone, adresse, ice, if_fournisseur, date_creation FROM fournisseurs ORDER BY code ASC")
        suppliers = cursor.fetchall()
        conn.close()
        return suppliers

    def show_add_supplier_dialog(self):
        if hasattr(self, 'add_supplier_win') and self.add_supplier_win.winfo_exists():
            self.add_supplier_win.focus()
            return

        self.add_supplier_win = ctk.CTkToplevel(self)
        self.add_supplier_win.title("Ajouter un Nouveau Fournisseur")
        self.add_supplier_win.geometry("450x450")
        self.add_supplier_win.transient(self)

        frame = ctk.CTkFrame(self.add_supplier_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Générer automatiquement le code fournisseur
        auto_code = self.generate_next_supplier_code()

        fields = {
            "Code:": ("code_entry", auto_code, True),  # (field_name, default_value, readonly)
            "Nom:": ("name_entry", "", False),
            "Téléphone:": ("phone_entry", "", False),
            "Adresse:": ("address_entry", "", False),
            "ICE:": ("ice_entry", "", False),
            "IF:": ("if_entry", "", False)
        }

        self.supplier_entries = {}
        for i, (text, field_info) in enumerate(fields.items()):
            field_name, default_value, readonly = field_info

            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            entry = ctk.CTkEntry(frame, width=250)
            entry.grid(row=i, column=1, padx=10, pady=5)

            if default_value:
                entry.insert(0, default_value)

            if readonly:
                entry.configure(state="disabled")

            self.supplier_entries[field_name] = entry

        save_button = ctk.CTkButton(frame, text="Enregistrer", command=self.save_new_supplier)
        save_button.grid(row=len(fields), column=0, columnspan=2, pady=20)

    def save_new_supplier(self):
        name = self.supplier_entries["name_entry"].get()
        if not name:
            print("Error: Name is required")
            return

        # Obtenir le code (généré automatiquement)
        code = self.supplier_entries["code_entry"].get()
        if not code:
            code = self.generate_next_supplier_code()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO fournisseurs (code, nom, telephone, adresse, ice, if_fournisseur)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                code,
                name,
                self.supplier_entries["phone_entry"].get(),
                self.supplier_entries["address_entry"].get(),
                self.supplier_entries["ice_entry"].get(),
                self.supplier_entries["if_entry"].get()
            ))
            conn.commit()
            print(f"✅ Fournisseur '{name}' ajouté avec succès avec le code '{code}'")

            self.add_supplier_win.destroy()
            self.show_suppliers_page()  # Refresh the suppliers page

        except sqlite3.IntegrityError as e:
            print(f"❌ Erreur: Le code '{code}' existe déjà. Génération d'un nouveau code...")
            # Générer un nouveau code et réessayer
            new_code = self.generate_next_supplier_code()
            cursor.execute("""
                INSERT INTO fournisseurs (code, nom, telephone, adresse, ice, if_fournisseur)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                new_code,
                name,
                self.supplier_entries["phone_entry"].get(),
                self.supplier_entries["address_entry"].get(),
                self.supplier_entries["ice_entry"].get(),
                self.supplier_entries["if_entry"].get()
            ))
            conn.commit()
            print(f"✅ Fournisseur '{name}' ajouté avec succès avec le code '{new_code}'")

            self.add_supplier_win.destroy()
            self.show_suppliers_page()  # Refresh the suppliers page

        finally:
            conn.close()

    # --- Nouvelles fonctions pour les modules ---

    def create_invoices_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Numéro", "Client", "Date", "Montant TTC", "Statut"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_invoice_id = None
        # Affichage des factures existantes
        invoices_data = self.get_all_invoices()

        # حفظ بيانات الفواتير لاستخدامها في قائمة السياق
        self.invoices_table_data = {}

        for row_idx, invoice in enumerate(invoices_data, start=1):
            (id, numero, client_id, date_facture, montant_ht, montant_tva, montant_ttc, statut) = invoice
            client_name = self.get_client_name(client_id) if client_id else "Client non spécifié"

            # حفظ بيانات الفاتورة
            self.invoices_table_data[row_idx] = {
                'id': id,
                'numero': numero,
                'client_id': client_id,
                'client_name': client_name,
                'date_facture': date_facture,
                'montant_ht': montant_ht,
                'montant_tva': montant_tva,
                'montant_ttc': montant_ttc,
                'statut': statut
            }

            display_values = [
                numero or "",
                client_name,
                date_facture or "",
                f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH",
                statut or "En attente"
            ]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_invoice_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_invoices_context_menu)

    def show_invoice_context_menu(self, event, row):
        """عرض قائمة السياق للفاتورة"""
        if row not in self.invoices_table_data:
            return

        invoice_data = self.invoices_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {invoice_data['numero']}",
            command=lambda: self.edit_invoice_from_context(invoice_data['id'])
        )

        context_menu.add_command(
            label=f"🖨️ طباعة {invoice_data['numero']}",
            command=lambda: self.print_invoice_from_context(invoice_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {invoice_data['numero']}",
            command=lambda: self.delete_invoice_from_context(invoice_data['id'], invoice_data['numero'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_invoices_context_menu(self, event):
        """عرض قائمة سياق عامة للفواتير"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة فاتورة جديدة",
            command=self.show_add_invoice_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_invoice_from_context(self, invoice_id):
        """تعديل فاتورة من قائمة السياق"""
        self.selected_invoice_id = invoice_id
        self.edit_invoice()

    def print_invoice_from_context(self, invoice_id):
        """طباعة فاتورة من قائمة السياق"""
        self.selected_invoice_id = invoice_id
        self.print_invoice()

    def delete_invoice_from_context(self, invoice_id, invoice_numero):
        """حذف فاتورة من قائمة السياق"""
        self.selected_invoice_id = invoice_id
        self.delete_invoice()

    def edit_invoice_from_button(self, invoice_id):
        """تعديل فاتورة من زر الإجراء"""
        self.selected_invoice_id = invoice_id
        self.edit_invoice()

    def print_invoice_from_button(self, invoice_id):
        """طباعة فاتورة من زر الإجراء"""
        self.selected_invoice_id = invoice_id
        self.print_invoice()

    def delete_invoice_from_button(self, invoice_id, invoice_numero):
        """حذف فاتورة من زر الإجراء"""
        self.selected_invoice_id = invoice_id
        self.delete_invoice()

    def create_purchase_orders_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Numéro", "Fournisseur", "Date", "Montant", "Statut"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_purchase_order_id = None
        # Exemple de données
        sample_data = [
            (1, "En cours", "1500.00 DH", "2024-01-15", "Fournisseur A", "BC001"),
            (2, "Livré", "2300.00 DH", "2024-01-10", "Fournisseur B", "BC002")
        ]

        # حفظ بيانات أوامر الشراء لاستخدامها في قائمة السياق
        self.purchase_orders_table_data = {}

        for row_idx, data in enumerate(sample_data, start=1):
            po_id = data[0]

            # حفظ بيانات أمر الشراء
            self.purchase_orders_table_data[row_idx] = {
                'id': po_id,
                'statut': data[1],
                'montant': data[2],
                'date': data[3],
                'fournisseur': data[4],
                'numero': data[5]
            }

            # Afficher les données dans l'ordre: Numéro, Fournisseur, Date, Montant, Statut
            display_values = [data[5], data[4], data[3], data[2], data[1]]  # numero, fournisseur, date, montant, statut

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_purchase_order_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_purchase_orders_context_menu)

    def show_purchase_order_context_menu(self, event, row):
        """عرض قائمة السياق لأمر الشراء"""
        if row not in self.purchase_orders_table_data:
            return

        po_data = self.purchase_orders_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {po_data['numero']}",
            command=lambda: self.edit_purchase_order_from_context(po_data['id'])
        )

        context_menu.add_command(
            label=f"🔄 تحويل إلى فاتورة {po_data['numero']}",
            command=lambda: self.convert_to_invoice_from_context(po_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {po_data['numero']}",
            command=lambda: self.delete_purchase_order_from_context(po_data['id'], po_data['numero'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_purchase_orders_context_menu(self, event):
        """عرض قائمة سياق عامة لأوامر الشراء"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة أمر شراء جديد",
            command=self.show_add_purchase_order_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_purchase_order_from_context(self, po_id):
        """تعديل أمر شراء من قائمة السياق"""
        self.selected_purchase_order_id = po_id
        self.edit_purchase_order()

    def convert_to_invoice_from_context(self, po_id):
        """تحويل أمر شراء إلى فاتورة من قائمة السياق"""
        self.selected_purchase_order_id = po_id
        self.convert_to_invoice()

    def delete_purchase_order_from_context(self, po_id, po_numero):
        """حذف أمر شراء من قائمة السياق"""
        self.selected_purchase_order_id = po_id
        self.delete_purchase_order()

    def create_delivery_notes_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Numéro BL", "Client", "Date Livraison", "Statut"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_delivery_note_id = None
        # Exemple de données
        sample_data = [
            (1, "Livré", "2024-01-16", "Client A", "BL001"),
            (2, "En cours", "2024-01-18", "Client B", "BL002")
        ]

        # حفظ بيانات إشعارات التسليم لاستخدامها في قائمة السياق
        self.delivery_notes_table_data = {}

        for row_idx, data in enumerate(sample_data, start=1):
            dn_id = data[0]

            # حفظ بيانات إشعار التسليم
            self.delivery_notes_table_data[row_idx] = {
                'id': dn_id,
                'statut': data[1],
                'date_livraison': data[2],
                'client': data[3],
                'numero': data[4]
            }

            # Afficher les données dans l'ordre: Numéro BL, Client, Date Livraison, Statut
            display_values = [data[4], data[3], data[2], data[1]]  # numero, client, date_livraison, statut

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_delivery_note_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_delivery_notes_context_menu)

    def show_delivery_note_context_menu(self, event, row):
        """عرض قائمة السياق لإشعار التسليم"""
        if row not in self.delivery_notes_table_data:
            return

        dn_data = self.delivery_notes_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {dn_data['numero']}",
            command=lambda: self.edit_delivery_note_from_context(dn_data['id'])
        )

        context_menu.add_command(
            label=f"🖨️ طباعة {dn_data['numero']}",
            command=lambda: self.print_delivery_note_from_context(dn_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {dn_data['numero']}",
            command=lambda: self.delete_delivery_note_from_context(dn_data['id'], dn_data['numero'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_delivery_notes_context_menu(self, event):
        """عرض قائمة سياق عامة لإشعارات التسليم"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة إشعار تسليم جديد",
            command=self.show_add_delivery_note_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_delivery_note_from_context(self, dn_id):
        """تعديل إشعار تسليم من قائمة السياق"""
        self.selected_delivery_note_id = dn_id
        self.edit_delivery_note()

    def print_delivery_note_from_context(self, dn_id):
        """طباعة إشعار تسليم من قائمة السياق"""
        self.selected_delivery_note_id = dn_id
        self.print_delivery_note()

    def delete_delivery_note_from_context(self, dn_id, dn_numero):
        """حذف إشعار تسليم من قائمة السياق"""
        self.selected_delivery_note_id = dn_id
        self.delete_delivery_note()

    def create_stock_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Code", "Désignation", "Prix Vente", "Stock", "Valeur", "Alerte"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_stock_item_id = None
        products_data = self.get_all_products()

        # حفظ بيانات المخزون لاستخدامها في قائمة السياق
        self.stock_table_data = {}

        for row_idx, product in enumerate(products_data, start=1):
            (id, code, designation, prix_achat, prix_vente, stock, fournisseur, date_creation) = product

            # حفظ بيانات عنصر المخزون
            self.stock_table_data[row_idx] = {
                'id': id,
                'code': code,
                'designation': designation,
                'prix_achat': prix_achat,
                'prix_vente': prix_vente,
                'stock': stock,
                'fournisseur': fournisseur
            }

            # Alerte stock faible
            alert = "⚠️" if stock < 5 else "✅"
            valeur_stock = stock * prix_achat if prix_achat else 0

            display_values = [
                code,
                designation,
                self.format_currency(prix_vente) if prix_vente else self.format_currency(0),
                str(stock),
                self.format_currency(valeur_stock),
                alert
            ]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_stock_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_stock_context_menu)

    def show_stock_context_menu(self, event, row):
        """عرض قائمة السياق لعنصر المخزون"""
        if row not in self.stock_table_data:
            return

        stock_data = self.stock_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"📝 تحديث مخزون {stock_data['designation']}",
            command=lambda: self.update_stock_from_context(stock_data['id'])
        )

        context_menu.add_command(
            label=f"📈 عرض حركات {stock_data['designation']}",
            command=lambda: self.view_stock_movements_from_context(stock_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"✏️ تعديل معلومات {stock_data['designation']}",
            command=lambda: self.edit_product_from_stock_context(stock_data['id'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_stock_context_menu(self, event):
        """عرض قائمة سياق عامة للمخزون"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"🔄 تحديث جميع المخزون",
            command=self.update_stock
        )

        context_menu.add_command(
            label=f"📄 تصدير المخزون",
            command=self.export_stock
        )

        context_menu.add_command(
            label=f"📋 بدء جرد المخزون",
            command=self.start_inventory
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def update_stock_from_context(self, product_id):
        """تحديث مخزون منتج معين من قائمة السياق"""
        self.selected_stock_item_id = product_id
        print(f"تحديث مخزون المنتج {product_id}...")

    def view_stock_movements_from_context(self, product_id):
        """عرض حركات مخزون منتج معين من قائمة السياق"""
        self.selected_stock_item_id = product_id
        print(f"عرض حركات مخزون المنتج {product_id}...")

    def edit_product_from_stock_context(self, product_id):
        """تعديل معلومات منتج من قائمة سياق المخزون"""
        self.selected_product_id = product_id
        self.edit_product()

    def create_cash_movements_table(self, parent):
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ["Date", "Description", "Type", "Montant", "Solde"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        self.selected_cash_movement_id = None
        # Exemple de mouvements
        sample_movements = [
            (1, "1500.00 DH", "+500.00 DH", "Entrée", "2024-01-15", "Vente produit"),
            (2, "1000.00 DH", "-500.00 DH", "Sortie", "2024-01-14", "Achat fournitures")
        ]

        for row_idx, movement in enumerate(sample_movements, start=1):
            movement_id = movement[0]

            # Afficher les données dans l'ordre: Date, Description, Type, Montant, Solde
            display_values = [movement[4], movement[5], movement[3], movement[2], movement[1]]  # date, description, type, montant, solde

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

    def create_tva_summary(self, parent):
        summary_frame = ctk.CTkFrame(parent)
        summary_frame.pack(fill="both", expand=True, pady=10)

        title_label = ctk.CTkLabel(summary_frame, text="Résumé TVA", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Statistiques TVA
        stats_frame = ctk.CTkFrame(summary_frame)
        stats_frame.pack(fill="x", padx=20, pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2), weight=1)

        self.create_stat_card(stats_frame, "TVA Collectée", "1200.00 DH", "#22c55e", 0)
        self.create_stat_card(stats_frame, "TVA Déductible", "800.00 DH", "#3b82f6", 1)
        self.create_stat_card(stats_frame, "TVA à Payer", "400.00 DH", "#ef4444", 2)

    # --- Fonctions utilitaires pour les nouveaux modules ---

    def get_all_invoices(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM factures ORDER BY date_facture DESC")
        invoices = cursor.fetchall()
        conn.close()
        return invoices

    def get_client_name(self, client_id):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT nom FROM clients WHERE id = ?", (client_id,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else "Client inconnu"

    def get_low_stock_count(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM produits WHERE stock < 5")
        count = cursor.fetchone()[0]
        conn.close()
        return count

    def get_total_stock_value(self):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT SUM(stock * prix_achat) FROM produits WHERE prix_achat IS NOT NULL")
        result = cursor.fetchone()[0]
        conn.close()
        return result if result else 0.0

    def get_cash_balance(self):
        # Simulation d'un solde de caisse
        return 1500.00

    # --- Fonctions de dialogue pour les nouveaux modules ---

    def show_add_invoice_dialog(self):
        if hasattr(self, 'add_invoice_win') and self.add_invoice_win.winfo_exists():
            self.add_invoice_win.focus()
            return

        self.add_invoice_win = ctk.CTkToplevel(self)
        self.add_invoice_win.title("Nouvelle Facture")
        self.add_invoice_win.geometry("600x500")
        self.add_invoice_win.transient(self)

        frame = ctk.CTkFrame(self.add_invoice_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Titre
        title_label = ctk.CTkLabel(frame, text="Créer une Nouvelle Facture", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Numéro Facture:": "numero_entry",
            "Date Facture:": "date_entry",
            "Montant HT:": "montant_ht_entry",
            "Montant TVA:": "montant_tva_entry",
            "Montant TTC:": "montant_ttc_entry"
        }

        self.invoice_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            # إنشاء حقل تاريخ محسن لحقول التواريخ
            if "Date" in text:
                entry = create_enhanced_date_entry(frame, "اختر تاريخ 📅", width=250)
            else:
                entry = ctk.CTkEntry(frame, width=250)

            entry.grid(row=i, column=1, padx=10, pady=5)
            self.invoice_entries[name] = entry

        # Client selection
        client_label = ctk.CTkLabel(frame, text="Client:")
        client_label.grid(row=len(fields)+1, column=0, padx=10, pady=5, sticky="w")

        clients_data = self.get_all_clients()
        client_names = [f"{client[1]} (ID: {client[0]})" for client in clients_data]
        self.client_combo = ctk.CTkComboBox(frame, values=client_names, width=250)
        self.client_combo.grid(row=len(fields)+1, column=1, padx=10, pady=5)

        # Statut
        statut_label = ctk.CTkLabel(frame, text="Statut:")
        statut_label.grid(row=len(fields)+2, column=0, padx=10, pady=5, sticky="w")
        self.statut_combo = ctk.CTkComboBox(frame, values=["En attente", "Payée", "Annulée"], width=250)
        self.statut_combo.set("En attente")
        self.statut_combo.grid(row=len(fields)+2, column=1, padx=10, pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+3, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.save_new_invoice)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_invoice_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def save_new_invoice(self):
        numero = self.invoice_entries["numero_entry"].get()
        if not numero:
            print("Erreur: Le numéro de facture est requis")
            return

        # Extraire l'ID du client sélectionné
        client_selection = self.client_combo.get()
        if not client_selection:
            print("Erreur: Veuillez sélectionner un client")
            return

        client_id = int(client_selection.split("ID: ")[1].split(")")[0])

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO factures (numero, client_id, date_facture, montant_ht, montant_tva, montant_ttc, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                numero,
                client_id,
                self.invoice_entries["date_entry"].get() or "2024-01-01",
                float(self.invoice_entries["montant_ht_entry"].get() or 0),
                float(self.invoice_entries["montant_tva_entry"].get() or 0),
                float(self.invoice_entries["montant_ttc_entry"].get() or 0),
                self.statut_combo.get()
            ))
            conn.commit()
            print(f"Facture {numero} créée avec succès!")
        except sqlite3.Error as e:
            print(f"Erreur de base de données: {e}")
        except ValueError as e:
            print(f"Erreur de valeur: {e}")
        finally:
            if conn:
                conn.close()

        self.add_invoice_win.destroy()
        self.show_invoices_page()

    def show_add_purchase_order_dialog(self):
        if hasattr(self, 'add_po_win') and self.add_po_win.winfo_exists():
            self.add_po_win.focus()
            return

        self.add_po_win = ctk.CTkToplevel(self)
        self.add_po_win.title("Nouveau Bon de Commande")
        self.add_po_win.geometry("500x400")
        self.add_po_win.transient(self)

        frame = ctk.CTkFrame(self.add_po_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Créer un Nouveau Bon de Commande", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Numéro BC:": "numero_entry",
            "Date:": "date_entry",
            "Fournisseur:": "fournisseur_entry",
            "Montant:": "montant_entry",
            "Description:": "description_entry"
        }

        self.po_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            # إنشاء حقل تاريخ محسن لحقول التواريخ
            if "Date" in text:
                entry = create_enhanced_date_entry(frame, "اختر تاريخ 📅", width=250)
            else:
                entry = ctk.CTkEntry(frame, width=250)

            entry.grid(row=i, column=1, padx=10, pady=5)
            self.po_entries[name] = entry

        # Statut
        statut_label = ctk.CTkLabel(frame, text="Statut:")
        statut_label.grid(row=len(fields)+1, column=0, padx=10, pady=5, sticky="w")
        self.po_statut_combo = ctk.CTkComboBox(frame, values=["En cours", "Livré", "Annulé"], width=250)
        self.po_statut_combo.set("En cours")
        self.po_statut_combo.grid(row=len(fields)+1, column=1, padx=10, pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+2, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.save_new_purchase_order)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_po_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def save_new_purchase_order(self):
        numero = self.po_entries["numero_entry"].get()
        if not numero:
            print("Erreur: Le numéro de bon de commande est requis")
            return

        print(f"Bon de commande {numero} créé avec succès!")
        self.add_po_win.destroy()
        self.show_purchase_orders_page()

    def show_add_delivery_note_dialog(self):
        if hasattr(self, 'add_dn_win') and self.add_dn_win.winfo_exists():
            self.add_dn_win.focus()
            return

        self.add_dn_win = ctk.CTkToplevel(self)
        self.add_dn_win.title("Nouveau Bon de Livraison")
        self.add_dn_win.geometry("500x400")
        self.add_dn_win.transient(self)

        frame = ctk.CTkFrame(self.add_dn_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Créer un Nouveau Bon de Livraison", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Numéro BL:": "numero_entry",
            "Date Livraison:": "date_entry",
            "Client:": "client_entry",
            "Adresse Livraison:": "adresse_entry",
            "Description:": "description_entry"
        }

        self.dn_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            # إنشاء حقل تاريخ محسن لحقول التواريخ
            if "Date" in text:
                entry = create_enhanced_date_entry(frame, "اختر تاريخ 📅", width=250)
            else:
                entry = ctk.CTkEntry(frame, width=250)

            entry.grid(row=i, column=1, padx=10, pady=5)
            self.dn_entries[name] = entry

        # Statut
        statut_label = ctk.CTkLabel(frame, text="Statut:")
        statut_label.grid(row=len(fields)+1, column=0, padx=10, pady=5, sticky="w")
        self.dn_statut_combo = ctk.CTkComboBox(frame, values=["En cours", "Livré", "Annulé"], width=250)
        self.dn_statut_combo.set("En cours")
        self.dn_statut_combo.grid(row=len(fields)+1, column=1, padx=10, pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+2, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.save_new_delivery_note)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_dn_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def save_new_delivery_note(self):
        numero = self.dn_entries["numero_entry"].get()
        if not numero:
            print("Erreur: Le numéro de bon de livraison est requis")
            return

        print(f"Bon de livraison {numero} créé avec succès!")
        self.add_dn_win.destroy()
        self.show_delivery_notes_page()

    def show_add_cash_entry_dialog(self):
        if hasattr(self, 'add_cash_entry_win') and self.add_cash_entry_win.winfo_exists():
            self.add_cash_entry_win.focus()
            return

        self.add_cash_entry_win = ctk.CTkToplevel(self)
        self.add_cash_entry_win.title("Ajouter Entrée de Caisse")
        self.add_cash_entry_win.geometry("400x300")
        self.add_cash_entry_win.transient(self)

        frame = ctk.CTkFrame(self.add_cash_entry_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Nouvelle Entrée de Caisse", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Montant:": "montant_entry",
            "Description:": "description_entry",
            "Date:": "date_entry"
        }

        self.cash_entry_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            # إنشاء حقل تاريخ محسن لحقول التواريخ
            if "Date" in text:
                entry = create_enhanced_date_entry(frame, "اختر تاريخ 📅", width=250)
            else:
                entry = ctk.CTkEntry(frame, width=250)

            entry.grid(row=i, column=1, padx=10, pady=5)
            self.cash_entry_entries[name] = entry

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.save_cash_entry)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_cash_entry_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def save_cash_entry(self):
        montant = self.cash_entry_entries["montant_entry"].get()
        if not montant:
            print("Erreur: Le montant est requis")
            return

        print(f"Entrée de caisse de {montant} DH enregistrée avec succès!")
        self.add_cash_entry_win.destroy()
        self.show_cash_page()

    def show_add_cash_exit_dialog(self):
        if hasattr(self, 'add_cash_exit_win') and self.add_cash_exit_win.winfo_exists():
            self.add_cash_exit_win.focus()
            return

        self.add_cash_exit_win = ctk.CTkToplevel(self)
        self.add_cash_exit_win.title("Ajouter Sortie de Caisse")
        self.add_cash_exit_win.geometry("400x300")
        self.add_cash_exit_win.transient(self)

        frame = ctk.CTkFrame(self.add_cash_exit_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Nouvelle Sortie de Caisse", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Montant:": "montant_entry",
            "Description:": "description_entry",
            "Date:": "date_entry"
        }

        self.cash_exit_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")

            # إنشاء حقل تاريخ محسن لحقول التواريخ
            if "Date" in text:
                entry = create_enhanced_date_entry(frame, "اختر تاريخ 📅", width=250)
            else:
                entry = ctk.CTkEntry(frame, width=250)

            entry.grid(row=i, column=1, padx=10, pady=5)
            self.cash_exit_entries[name] = entry

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.save_cash_exit)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.add_cash_exit_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def save_cash_exit(self):
        montant = self.cash_exit_entries["montant_entry"].get()
        if not montant:
            print("Erreur: Le montant est requis")
            return

        print(f"Sortie de caisse de {montant} DH enregistrée avec succès!")
        self.add_cash_exit_win.destroy()
        self.show_cash_page()

    # --- Fonctions de génération de rapports ---

    def generate_sales_report(self):
        print("Génération du rapport des ventes...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'sales_report_win') and self.sales_report_win.winfo_exists():
            self.sales_report_win.focus()
            return

        self.sales_report_win = ctk.CTkToplevel(self)
        self.sales_report_win.title("Rapport des Ventes")
        self.sales_report_win.geometry("600x400")
        self.sales_report_win.transient(self)

        frame = ctk.CTkFrame(self.sales_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport des Ventes", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        invoices_data = self.get_all_invoices()
        total_sales = sum(invoice[6] for invoice in invoices_data if invoice[6])  # montant_ttc

        report_content = f"""RAPPORT DES VENTES
{'='*50}

Nombre total de factures: {len(invoices_data)}
Chiffre d'affaires total: {total_sales:.2f} DH

Détail des factures:
{'-'*30}
"""

        for invoice in invoices_data[:10]:  # Afficher les 10 premières
            numero, client_id, date_facture, montant_ttc = invoice[1], invoice[2], invoice[3], invoice[6]
            client_name = self.get_client_name(client_id) if client_id else "Client inconnu"
            report_content += f"Facture {numero} - {client_name} - {date_facture} - {montant_ttc:.2f} DH\n"

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def generate_purchases_report(self):
        print("Génération du rapport des achats...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'purchases_report_win') and self.purchases_report_win.winfo_exists():
            self.purchases_report_win.focus()
            return

        self.purchases_report_win = ctk.CTkToplevel(self)
        self.purchases_report_win.title("Rapport des Achats")
        self.purchases_report_win.geometry("600x400")
        self.purchases_report_win.transient(self)

        frame = ctk.CTkFrame(self.purchases_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport des Achats", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        products_data = self.get_all_products()
        total_purchases = sum(product[3] * product[5] for product in products_data if product[3] and product[5])  # prix_achat * stock

        report_content = f"""RAPPORT DES ACHATS
{'='*50}

Nombre total de produits: {len(products_data)}
Valeur totale des achats: {total_purchases:.2f} DH

Détail des produits:
{'-'*30}
"""

        for product in products_data[:10]:  # Afficher les 10 premiers
            code, designation, prix_achat, stock = product[1], product[2], product[3], product[5]
            valeur = (prix_achat or 0) * (stock or 0)
            report_content += f"{code} - {designation} - Stock: {stock} - Valeur: {valeur:.2f} DH\n"

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def generate_stock_report(self):
        print("Génération du rapport de stock...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'stock_report_win') and self.stock_report_win.winfo_exists():
            self.stock_report_win.focus()
            return

        self.stock_report_win = ctk.CTkToplevel(self)
        self.stock_report_win.title("Rapport de Stock")
        self.stock_report_win.geometry("600x400")
        self.stock_report_win.transient(self)

        frame = ctk.CTkFrame(self.stock_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport de Stock", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        products_data = self.get_all_products()
        low_stock_products = [p for p in products_data if (p[5] or 0) < 5]  # stock < 5
        total_value = self.get_total_stock_value()

        report_content = f"""RAPPORT DE STOCK
{'='*50}

Nombre total de produits: {len(products_data)}
Produits en stock faible: {len(low_stock_products)}
Valeur totale du stock: {total_value:.2f} DH

Produits en stock faible:
{'-'*30}
"""

        for product in low_stock_products:
            code, designation, stock = product[1], product[2], product[5] or 0
            report_content += f"{code} - {designation} - Stock: {stock}\n"

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def generate_financial_report(self):
        print("Génération du rapport financier...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'financial_report_win') and self.financial_report_win.winfo_exists():
            self.financial_report_win.focus()
            return

        self.financial_report_win = ctk.CTkToplevel(self)
        self.financial_report_win.title("Rapport Financier")
        self.financial_report_win.geometry("600x400")
        self.financial_report_win.transient(self)

        frame = ctk.CTkFrame(self.financial_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport Financier", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        invoices_data = self.get_all_invoices()
        total_revenue = sum(invoice[6] for invoice in invoices_data if invoice[6])  # montant_ttc
        total_tva = sum(invoice[5] for invoice in invoices_data if invoice[5])  # montant_tva
        cash_balance = self.get_cash_balance()

        report_content = f"""RAPPORT FINANCIER
{'='*50}

Chiffre d'affaires: {total_revenue:.2f} DH
TVA collectée: {total_tva:.2f} DH
Solde de caisse: {cash_balance:.2f} DH
Bénéfice estimé: {total_revenue * 0.2:.2f} DH

Résumé mensuel:
{'-'*30}
Janvier 2024: {total_revenue * 0.3:.2f} DH
Février 2024: {total_revenue * 0.4:.2f} DH
Mars 2024: {total_revenue * 0.3:.2f} DH
"""

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def generate_tva_report(self):
        print("Génération du rapport TVA...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'tva_report_win') and self.tva_report_win.winfo_exists():
            self.tva_report_win.focus()
            return

        self.tva_report_win = ctk.CTkToplevel(self)
        self.tva_report_win.title("Rapport TVA")
        self.tva_report_win.geometry("600x400")
        self.tva_report_win.transient(self)

        frame = ctk.CTkFrame(self.tva_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport TVA", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        invoices_data = self.get_all_invoices()
        total_tva_collectee = sum(invoice[5] for invoice in invoices_data if invoice[5])  # montant_tva
        tva_deductible = total_tva_collectee * 0.6  # Simulation
        tva_a_payer = total_tva_collectee - tva_deductible

        report_content = f"""RAPPORT TVA
{'='*50}

TVA Collectée: {total_tva_collectee:.2f} DH
TVA Déductible: {tva_deductible:.2f} DH
TVA à Payer: {tva_a_payer:.2f} DH

Détail par facture:
{'-'*30}
"""

        for invoice in invoices_data[:10]:  # Afficher les 10 premières
            numero, montant_tva = invoice[1], invoice[5] or 0
            report_content += f"Facture {numero} - TVA: {montant_tva:.2f} DH\n"

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def generate_clients_report(self):
        print("Génération du rapport clients...")
        # Créer une fenêtre de rapport
        if hasattr(self, 'clients_report_win') and self.clients_report_win.winfo_exists():
            self.clients_report_win.focus()
            return

        self.clients_report_win = ctk.CTkToplevel(self)
        self.clients_report_win.title("Rapport Clients")
        self.clients_report_win.geometry("600x400")
        self.clients_report_win.transient(self)

        frame = ctk.CTkFrame(self.clients_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Rapport Clients", font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=10)

        # Contenu du rapport
        report_text = ctk.CTkTextbox(frame, width=550, height=300)
        report_text.pack(pady=10)

        # Générer le contenu du rapport
        clients_data = self.get_all_clients()
        invoices_data = self.get_all_invoices()

        report_content = f"""RAPPORT CLIENTS
{'='*50}

Nombre total de clients: {len(clients_data)}
Nombre total de factures: {len(invoices_data)}

Liste des clients:
{'-'*30}
"""

        for client in clients_data:
            client_id, nom, telephone, adresse = client[0], client[1], client[2], client[3]
            # Compter les factures pour ce client
            client_invoices = [inv for inv in invoices_data if inv[2] == client_id]
            total_client = sum(inv[6] for inv in client_invoices if inv[6])
            report_content += f"{nom} - Tél: {telephone or 'N/A'} - Factures: {len(client_invoices)} - Total: {total_client:.2f} DH\n"

        report_text.insert("0.0", report_content)
        report_text.configure(state="disabled")

        # Bouton d'exportation
        export_btn = ctk.CTkButton(frame, text="Exporter en PDF", command=lambda: print("Export PDF en cours..."))
        export_btn.pack(pady=10)

    def perform_search(self):
        print("Exécution de la recherche...")
        # Simuler une recherche simple
        search_term = "test"  # En réalité, cela viendrait de l'interface

        # Rechercher dans les clients
        clients_data = self.get_all_clients()
        matching_clients = [c for c in clients_data if search_term.lower() in c[1].lower()]

        # Rechercher dans les produits
        products_data = self.get_all_products()
        matching_products = [p for p in products_data if search_term.lower() in p[2].lower()]

        print(f"Recherche pour '{search_term}':")
        print(f"Clients trouvés: {len(matching_clients)}")
        print(f"Produits trouvés: {len(matching_products)}")

    def clear_search(self):
        print("Effacement des résultats de recherche...")
        # Effacer les résultats de recherche
        print("Résultats de recherche effacés")

    def show_advanced_search(self):
        print("Ouverture de la recherche avancée...")
        if hasattr(self, 'advanced_search_win') and self.advanced_search_win.winfo_exists():
            self.advanced_search_win.focus()
            return

        self.advanced_search_win = ctk.CTkToplevel(self)
        self.advanced_search_win.title("Recherche Avancée")
        self.advanced_search_win.geometry("500x400")
        self.advanced_search_win.transient(self)

        frame = ctk.CTkFrame(self.advanced_search_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Recherche Avancée", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(pady=10)

        # Critères de recherche
        criteria_frame = ctk.CTkFrame(frame)
        criteria_frame.pack(fill="x", pady=10)

        # Type de recherche
        type_label = ctk.CTkLabel(criteria_frame, text="Type:")
        type_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        type_combo = ctk.CTkComboBox(criteria_frame, values=["Produits", "Factures", "Fournisseurs"])
        type_combo.grid(row=0, column=1, padx=10, pady=5)

        # Terme de recherche
        term_label = ctk.CTkLabel(criteria_frame, text="Terme:")
        term_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        term_entry = ctk.CTkEntry(criteria_frame, width=200)
        term_entry.grid(row=1, column=1, padx=10, pady=5)

        # Période
        period_label = ctk.CTkLabel(criteria_frame, text="Période:")
        period_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        period_combo = ctk.CTkComboBox(criteria_frame, values=["Tout", "Aujourd'hui", "Cette semaine", "Ce mois"])
        period_combo.grid(row=2, column=1, padx=10, pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        search_btn = ctk.CTkButton(buttons_frame, text="Rechercher", command=lambda: print("Recherche avancée en cours..."))
        search_btn.pack(side="left", padx=10)

        close_btn = ctk.CTkButton(buttons_frame, text="Fermer", command=self.advanced_search_win.destroy)
        close_btn.pack(side="left", padx=10)

    # --- Fonctions pour les boutons d'édition et suppression ---

    def edit_client(self):
        print("Modification du client sélectionné...")
        if not hasattr(self, 'selected_client_id') or self.selected_client_id is None:
            print("Veuillez d'abord sélectionner un client")
            return
        self.show_edit_client_dialog()

    def delete_client(self):
        print("Suppression du client sélectionné...")
        if not hasattr(self, 'selected_client_id') or self.selected_client_id is None:
            print("Veuillez d'abord sélectionner un client")
            return
        self.confirm_delete_client()

    def edit_supplier(self):
        print("Modification du fournisseur sélectionné...")
        if not hasattr(self, 'selected_supplier_id') or self.selected_supplier_id is None:
            print("Veuillez d'abord sélectionner un fournisseur")
            return
        self.show_edit_supplier_dialog()

    def delete_supplier(self):
        print("Suppression du fournisseur sélectionné...")
        if not hasattr(self, 'selected_supplier_id') or self.selected_supplier_id is None:
            print("Veuillez d'abord sélectionner un fournisseur")
            return
        self.confirm_delete_supplier()

    def edit_product(self):
        print("Modification du produit sélectionné...")
        if not hasattr(self, 'selected_product_id') or self.selected_product_id is None:
            print("Veuillez d'abord sélectionner un produit")
            return
        self.show_edit_product_dialog()

    def delete_product(self):
        print("Suppression du produit sélectionné...")
        if not hasattr(self, 'selected_product_id') or self.selected_product_id is None:
            print("Veuillez d'abord sélectionner un produit")
            return
        self.confirm_delete_product()

    def edit_invoice(self):
        print("Modification de la facture sélectionnée...")
        # Simuler la modification d'une facture
        invoices_data = self.get_all_invoices()
        if invoices_data:
            invoice = invoices_data[0]
            print(f"Modification de la facture {invoice[1]}")
        else:
            print("Aucune facture à modifier")

    def delete_invoice(self):
        print("Suppression de la facture sélectionnée...")
        if not hasattr(self, 'selected_invoice_id') or self.selected_invoice_id is None:
            print("Veuillez d'abord sélectionner une facture")
            return
        self.confirm_delete_invoice()

    def print_invoice(self):
        print("Impression de la facture...")
        # Simuler l'impression d'une facture
        invoices_data = self.get_all_invoices()
        if invoices_data:
            invoice = invoices_data[0]
            print(f"Impression de la facture {invoice[1]} en cours...")
            print("Facture envoyée à l'imprimante")
        else:
            print("Aucune facture à imprimer")

    def edit_purchase_order(self):
        print("Modification du bon de commande...")
        print("Dialogue de modification du bon de commande ouvert")

    def delete_purchase_order(self):
        print("Suppression du bon de commande...")
        if not hasattr(self, 'selected_purchase_order_id') or self.selected_purchase_order_id is None:
            print("Veuillez d'abord sélectionner un bon de commande")
            return
        self.confirm_delete_purchase_order()

    def convert_to_invoice(self):
        print("Conversion du bon de commande en facture...")
        print("Bon de commande converti en facture avec succès")

    def edit_delivery_note(self):
        print("Modification du bon de livraison...")
        if not hasattr(self, 'selected_delivery_note_id') or self.selected_delivery_note_id is None:
            print("Veuillez d'abord sélectionner un bon de livraison")
            return
        print(f"Modification du bon de livraison ID: {self.selected_delivery_note_id}")
        # هنا يمكن إضافة نافذة التعديل عندما تكون متوفرة
        print("نافذة تعديل بون التسليم مفتوحة")

    def delete_delivery_note(self):
        print("Suppression du bon de livraison...")
        if not hasattr(self, 'selected_delivery_note_id') or self.selected_delivery_note_id is None:
            print("Veuillez d'abord sélectionner un bon de livraison")
            return
        self.confirm_delete_delivery_note()

    def print_delivery_note(self):
        print("Impression du bon de livraison...")
        print("Bon de livraison envoyé à l'imprimante")

    def update_stock(self):
        print("Mise à jour du stock...")
        # Simuler la mise à jour du stock
        products_data = self.get_all_products()
        print(f"Mise à jour de {len(products_data)} produits en stock")
        print("Stock mis à jour avec succès")

    def export_stock(self):
        print("Exportation du stock...")
        # Simuler l'exportation du stock
        products_data = self.get_all_products()
        print(f"Exportation de {len(products_data)} produits")
        print("Stock exporté vers fichier Excel")

    def start_inventory(self):
        print("Démarrage de l'inventaire...")
        # Simuler le démarrage de l'inventaire
        products_data = self.get_all_products()
        print(f"Inventaire démarré pour {len(products_data)} produits")
        print("Processus d'inventaire en cours...")

    def calculate_prix_achat_ttc(self):
        """Calcule automatiquement le prix d'achat TTC et le montant TVA"""
        print("🔄 Fonction calculate_prix_achat_ttc appelée")
        
        try:
            # Vérifier que self.product_entries existe
            if not hasattr(self, 'product_entries'):
                print("❌ self.product_entries n'existe pas")
                return
                
            print(f"📋 Champs disponibles: {list(self.product_entries.keys())}")
            
            # Vérifier que les champs existent
            required_fields = ["prix_achat_entry", "tva_combo", "prix_achat_ttc_entry"]
            for field in required_fields:
                if field not in self.product_entries:
                    print(f"❌ Champ {field} non trouvé")
                    return
            
            print("✅ Tous les champs requis sont présents")
            
            # Récupérer le prix HT
            prix_ht_str = self.product_entries["prix_achat_entry"].get()
            print(f"📊 Prix HT saisi: '{prix_ht_str}'")
            
            if not prix_ht_str or prix_ht_str.strip() == "":
                # Utiliser 0 comme valeur par défaut
                prix_ht = 0.0
                print("⚠️ Prix HT vide, utilisation de 0")
            else:
                prix_ht = float(prix_ht_str)
                print(f"💰 Prix HT converti: {prix_ht}")

            # Récupérer le taux de TVA
            tva_text = self.product_entries["tva_combo"].get()
            print(f"📈 TVA sélectionnée: '{tva_text}'")
            tva_rate = float(tva_text.replace('%', '')) / 100
            print(f"📈 Taux TVA converti: {tva_rate}")

            # Calculer le prix TTC
            prix_ttc = prix_ht * (1 + tva_rate)
            print(f"💵 Prix TTC calculé: {prix_ttc}")

            # Mettre à jour le champ TTC
            ttc_entry = self.product_entries["prix_achat_ttc_entry"]
            ttc_entry.configure(state="normal")
            ttc_entry.delete(0, "end")
            ttc_entry.insert(0, f"{prix_ttc:.2f}")
            ttc_entry.configure(state="disabled")
            print(f"✅ Champ TTC mis à jour avec: {prix_ttc:.2f}")

        except (ValueError, KeyError) as e:
            print(f"❌ Erreur lors du calcul du prix TTC: {str(e)}")
        except Exception as e:
            print(f"❌ Erreur inattendue: {str(e)}")
            import traceback
            traceback.print_exc()

    def calculate_tva(self):
        print("Calcul de la TVA...")
        # Calculer la TVA
        invoices_data = self.get_all_invoices()
        total_tva = sum(invoice[5] for invoice in invoices_data if invoice[5])
        print(f"TVA totale calculée: {self.format_currency(total_tva)}")

    def export_tva(self):
        print("Exportation de la TVA...")
        # Exporter la TVA
        invoices_data = self.get_all_invoices()
        print(f"Exportation des données TVA pour {len(invoices_data)} factures")
        print("Données TVA exportées vers fichier Excel")

    def print_tva_declaration(self):
        print("Impression de la déclaration TVA...")
        # Imprimer la déclaration TVA
        invoices_data = self.get_all_invoices()
        total_tva = sum(invoice[5] for invoice in invoices_data if invoice[5])
        print(f"Impression de la déclaration TVA - Total: {total_tva:.2f} DH")
        print("Déclaration TVA envoyée à l'imprimante")

    def show_transfer_dialog(self):
        print("Ouverture du dialogue de virement...")
        if hasattr(self, 'transfer_win') and self.transfer_win.winfo_exists():
            self.transfer_win.focus()
            return

        self.transfer_win = ctk.CTkToplevel(self)
        self.transfer_win.title("Virement de Fonds")
        self.transfer_win.geometry("400x300")
        self.transfer_win.transient(self)

        frame = ctk.CTkFrame(self.transfer_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Virement de Fonds", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Montant:": "montant_entry",
            "De (Compte):": "from_entry",
            "Vers (Compte):": "to_entry",
            "Description:": "description_entry"
        }

        self.transfer_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")
            entry = ctk.CTkEntry(frame, width=250)
            entry.grid(row=i, column=1, padx=10, pady=5)
            self.transfer_entries[name] = entry

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        transfer_btn = ctk.CTkButton(buttons_frame, text="Effectuer Virement", command=self.execute_transfer)
        transfer_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(buttons_frame, text="Annuler", command=self.transfer_win.destroy)
        cancel_btn.pack(side="left", padx=10)

    def execute_transfer(self):
        montant = self.transfer_entries["montant_entry"].get()
        if not montant:
            print("Erreur: Le montant est requis")
            return

        print(f"Virement de {montant} DH effectué avec succès!")
        self.transfer_win.destroy()
        self.show_cash_page()

    def export_cash_movements(self):
        print("Exportation des mouvements de caisse...")
        # Simuler l'exportation des mouvements de caisse
        print("Mouvements de caisse exportés vers fichier Excel")
        print("Fichier sauvegardé: mouvements_caisse_2024.xlsx")

    def clear_search(self):
        print("Effacement des résultats de recherche...")
        # À implémenter

    def show_advanced_search(self):
        print("Ouverture de la recherche avancée...")
        # À implémenter

    def export_all_reports(self):
        print("Exportation de tous les rapports...")
        # Simuler l'exportation de tous les rapports
        reports = ["Ventes", "Achats", "Stock", "Financier", "TVA", "Fournisseurs"]
        for report in reports:
            print(f"Exportation du rapport {report}...")
        print("Tous les rapports exportés avec succès!")

    def schedule_reports(self):
        print("Programmation des rapports...")
        # Simuler la programmation des rapports
        print("Rapports programmés pour génération automatique:")
        print("- Rapport mensuel des ventes: 1er de chaque mois")
        print("- Rapport de stock: Chaque lundi")
        print("- Rapport TVA: Fin de trimestre")

    def create_custom_report(self):
        print("Création d'un rapport personnalisé...")
        if hasattr(self, 'custom_report_win') and self.custom_report_win.winfo_exists():
            self.custom_report_win.focus()
            return

        self.custom_report_win = ctk.CTkToplevel(self)
        self.custom_report_win.title("Rapport Personnalisé")
        self.custom_report_win.geometry("500x400")
        self.custom_report_win.transient(self)

        frame = ctk.CTkFrame(self.custom_report_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        title_label = ctk.CTkLabel(frame, text="Créer un Rapport Personnalisé", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(pady=10)

        # Options du rapport
        options_frame = ctk.CTkFrame(frame)
        options_frame.pack(fill="x", pady=10)

        # Type de données
        data_label = ctk.CTkLabel(options_frame, text="Type de données:")
        data_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        data_combo = ctk.CTkComboBox(options_frame, values=["Ventes", "Achats", "Stock", "Fournisseurs"])
        data_combo.grid(row=0, column=1, padx=10, pady=5)

        # Période
        period_label = ctk.CTkLabel(options_frame, text="Période:")
        period_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        period_combo = ctk.CTkComboBox(options_frame, values=["Aujourd'hui", "Cette semaine", "Ce mois", "Ce trimestre", "Cette année"])
        period_combo.grid(row=1, column=1, padx=10, pady=5)

        # Format
        format_label = ctk.CTkLabel(options_frame, text="Format:")
        format_label.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        format_combo = ctk.CTkComboBox(options_frame, values=["PDF", "Excel", "CSV"])
        format_combo.grid(row=2, column=1, padx=10, pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        generate_btn = ctk.CTkButton(buttons_frame, text="Générer", command=lambda: print("Rapport personnalisé généré!"))
        generate_btn.pack(side="left", padx=10)

        close_btn = ctk.CTkButton(buttons_frame, text="Fermer", command=self.custom_report_win.destroy)
        close_btn.pack(side="left", padx=10)

    def confirm_delete_client(self):
        # Utiliser le client sélectionné
        if not hasattr(self, 'selected_client_id') or self.selected_client_id is None:
            print("Aucun client sélectionné")
            return

        # Trouver le client sélectionné
        clients_data = self.get_all_clients()
        client = None
        for c in clients_data:
            if c[0] == self.selected_client_id:
                client = c
                break

        if not client:
            print("Client sélectionné introuvable")
            return

        client_id, nom = client[0], client[1]

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_win') and self.confirm_delete_win.winfo_exists():
            self.confirm_delete_win.focus()
            return

        self.confirm_delete_win = ctk.CTkToplevel(self)
        self.confirm_delete_win.title("Confirmer la suppression")
        self.confirm_delete_win.geometry("400x200")
        self.confirm_delete_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer le client:\n{nom}?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
            conn.commit()
            conn.close()
            print(f"Client {nom} supprimé avec succès!")

            # إعادة ترقيم جميع العملاء تلقائياً
            self.renumber_all_clients()

            self.confirm_delete_win.destroy()
            self.show_clients_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def confirm_delete_product(self):
        # Utiliser le produit sélectionné
        if not hasattr(self, 'selected_product_id') or self.selected_product_id is None:
            print("Aucun produit sélectionné")
            return

        # Trouver le produit sélectionné
        products_data = self.get_all_products()
        product = None
        for p in products_data:
            if p[0] == self.selected_product_id:
                product = p
                break

        if not product:
            print("Produit sélectionné introuvable")
            return

        product_id, code, designation = product[0], product[1], product[2]

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_product_win') and self.confirm_delete_product_win.winfo_exists():
            self.confirm_delete_product_win.focus()
            return

        self.confirm_delete_product_win = ctk.CTkToplevel(self)
        self.confirm_delete_product_win.title("Confirmer la suppression")
        self.confirm_delete_product_win.geometry("400x200")
        self.confirm_delete_product_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_product_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer le produit:\n{code} - {designation}?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM produits WHERE id = ?", (product_id,))
            conn.commit()
            conn.close()
            print(f"Produit {designation} supprimé avec succès!")

            # إعادة ترقيم جميع المنتجات تلقائياً
            self.renumber_all_products()

            self.confirm_delete_product_win.destroy()
            self.show_products_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_product_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def show_edit_supplier_dialog(self):
        # Utiliser le fournisseur sélectionné
        if not hasattr(self, 'selected_supplier_id') or self.selected_supplier_id is None:
            print("Aucun fournisseur sélectionné")
            return

        # Trouver le fournisseur sélectionné
        suppliers_data = self.get_all_suppliers()
        supplier = None
        for s in suppliers_data:
            if s[0] == self.selected_supplier_id:
                supplier = s
                break

        if not supplier:
            print("Fournisseur sélectionné introuvable")
            return

        # التعامل مع البيانات الجديدة مع عمود الكود
        if len(supplier) >= 8:
            supplier_id, code, nom, telephone, adresse, ice, if_fournisseur, date_creation = supplier[:8]
        else:
            supplier_id = supplier[0] if len(supplier) > 0 else None
            code = supplier[1] if len(supplier) > 1 else ""
            nom = supplier[2] if len(supplier) > 2 else ""
            telephone = supplier[3] if len(supplier) > 3 else ""
            adresse = supplier[4] if len(supplier) > 4 else ""
            ice = supplier[5] if len(supplier) > 5 else ""
            if_fournisseur = supplier[6] if len(supplier) > 6 else ""
            date_creation = supplier[7] if len(supplier) > 7 else ""

        if hasattr(self, 'edit_supplier_win') and self.edit_supplier_win.winfo_exists():
            self.edit_supplier_win.focus()
            return

        self.edit_supplier_win = ctk.CTkToplevel(self)
        self.edit_supplier_win.title(f"Modifier Fournisseur: {nom}")
        self.edit_supplier_win.geometry("400x400")
        self.edit_supplier_win.transient(self)

        frame = ctk.CTkFrame(self.edit_supplier_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Titre
        title_label = ctk.CTkLabel(frame, text=f"Modification du fournisseur: {nom}", font=ctk.CTkFont(size=16, weight="bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        fields = {
            "Code:": ("code_entry", True),  # (field_name, readonly)
            "Nom:": ("name_entry", False),
            "Téléphone:": ("phone_entry", False),
            "Adresse:": ("address_entry", False),
            "ICE:": ("ice_entry", False),
            "IF:": ("if_entry", False)
        }

        # Valeurs actuelles
        current_values = {
            "code_entry": code or "",
            "name_entry": nom,
            "phone_entry": telephone or "",
            "address_entry": adresse or "",
            "ice_entry": ice or "",
            "if_entry": if_fournisseur or ""
        }

        self.edit_supplier_entries = {}
        for i, (text, name) in enumerate(fields.items(), start=1):
            label = ctk.CTkLabel(frame, text=text)
            label.grid(row=i, column=0, padx=10, pady=5, sticky="w")
            entry = ctk.CTkEntry(frame, width=250)
            entry.insert(0, current_values[name])  # Pré-remplir avec les valeurs actuelles
            entry.grid(row=i, column=1, padx=10, pady=5)
            self.edit_supplier_entries[name] = entry

        # Stocker l'ID du fournisseur pour la mise à jour
        self.current_supplier_id = supplier_id

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)

        save_button = ctk.CTkButton(buttons_frame, text="Enregistrer", command=self.update_supplier)
        save_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.edit_supplier_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def update_supplier(self):
        """تحديث بيانات المورد مع التحقق من صحة البيانات"""
        try:
            # الحصول على البيانات من الحقول
            name = self.edit_supplier_entries["name_entry"].get().strip()
            phone = self.edit_supplier_entries["phone_entry"].get().strip()
            address = self.edit_supplier_entries["address_entry"].get().strip()
            ice = self.edit_supplier_entries["ice_entry"].get().strip()
            if_fournisseur = self.edit_supplier_entries["if_entry"].get().strip()

            # التحقق من صحة البيانات
            validation_errors = []

            # التحقق من الاسم (مطلوب)
            if not name:
                validation_errors.append("❌ اسم المورد مطلوب")
            elif len(name) < 2:
                validation_errors.append("❌ اسم المورد يجب أن يكون أكثر من حرفين")

            # التحقق من رقم الهاتف (اختياري ولكن إذا تم إدخاله يجب أن يكون صحيحاً)
            if phone and not self.validate_phone_number(phone):
                validation_errors.append("❌ رقم الهاتف غير صحيح")

            # التحقق من ICE (إذا تم إدخاله)
            if ice and len(ice) > 0 and len(ice) < 15:
                validation_errors.append("❌ رقم ICE يجب أن يكون 15 رقماً")

            # عرض أخطاء التحقق إن وجدت
            if validation_errors:
                error_message = "\n".join(validation_errors)
                print(f"⚠️ أخطاء في البيانات:\n{error_message}")

                # إنشاء نافذة تحذير
                self.show_validation_error(validation_errors)
                return

            # التحقق من عدم وجود مورد آخر بنفس الاسم
            if self.check_duplicate_supplier_name(name, self.current_supplier_id):
                print("❌ يوجد مورد آخر بنفس الاسم")
                self.show_error_dialog("خطأ", "يوجد مورد آخر بنفس الاسم. يرجى اختيار اسم مختلف.")
                return

            # تحديث قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE fournisseurs
                SET nom = ?, telephone = ?, adresse = ?, ice = ?, if_fournisseur = ?, date_modification = ?
                WHERE id = ?
            """, (
                name,
                phone,
                address,
                ice,
                if_fournisseur,
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                self.current_supplier_id
            ))

            # التحقق من نجاح التحديث
            if cursor.rowcount > 0:
                conn.commit()
                print(f"✅ تم تحديث المورد '{name}' بنجاح!")

                # إغلاق النافذة وتحديث القائمة
                self.edit_supplier_win.destroy()
                self.show_suppliers_page()

                # إظهار رسالة نجاح
                self.show_success_dialog("نجح التحديث", f"تم تحديث بيانات المورد '{name}' بنجاح!")
            else:
                print("❌ فشل في تحديث المورد")
                self.show_error_dialog("خطأ", "فشل في تحديث بيانات المورد. يرجى المحاولة مرة أخرى.")

            conn.close()

        except sqlite3.Error as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
            self.show_error_dialog("خطأ في قاعدة البيانات", f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}")
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            self.show_error_dialog("خطأ غير متوقع", f"حدث خطأ غير متوقع:\n{str(e)}")

    def validate_phone_number(self, phone):
        """التحقق من صحة رقم الهاتف"""
        if not phone:
            return True  # رقم الهاتف اختياري

        # إزالة المسافات والرموز
        phone_clean = phone.replace(" ", "").replace("-", "").replace("(", "").replace(")", "").replace("+", "")

        # التحقق من أن جميع الأحرف أرقام
        if not phone_clean.isdigit():
            return False

        # التحقق من طول الرقم (بين 8 و 15 رقم)
        if len(phone_clean) < 8 or len(phone_clean) > 15:
            return False

        return True

    def check_duplicate_supplier_name(self, name, current_id=None):
        """التحقق من عدم وجود مورد آخر بنفس الاسم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if current_id:
                # في حالة التحديث، استثناء المورد الحالي
                cursor.execute("SELECT COUNT(*) FROM fournisseurs WHERE nom = ? AND id != ?", (name, current_id))
            else:
                # في حالة الإضافة الجديدة
                cursor.execute("SELECT COUNT(*) FROM fournisseurs WHERE nom = ?", (name,))

            count = cursor.fetchone()[0]
            conn.close()

            return count > 0

        except Exception as e:
            print(f"❌ خطأ في التحقق من تكرار الاسم: {e}")
            return False

    def show_validation_error(self, errors):
        """عرض نافذة أخطاء التحقق"""
        error_window = ctk.CTkToplevel(self)
        error_window.title("أخطاء في البيانات")
        error_window.geometry("400x300")
        error_window.transient(self)
        error_window.grab_set()

        # عنوان
        title_label = ctk.CTkLabel(
            error_window,
            text="⚠️ يرجى تصحيح الأخطاء التالية:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=20)

        # قائمة الأخطاء
        errors_frame = ctk.CTkScrollableFrame(error_window)
        errors_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for error in errors:
            error_label = ctk.CTkLabel(
                errors_frame,
                text=error,
                font=ctk.CTkFont(size=12),
                text_color="red"
            )
            error_label.pack(anchor="w", pady=5)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            error_window,
            text="موافق",
            command=error_window.destroy,
            fg_color="red",
            hover_color="darkred"
        )
        close_btn.pack(pady=20)

    def show_error_dialog(self, title, message):
        """عرض نافذة خطأ"""
        error_window = ctk.CTkToplevel(self)
        error_window.title(title)
        error_window.geometry("400x200")
        error_window.transient(self)
        error_window.grab_set()

        # رسالة الخطأ
        message_label = ctk.CTkLabel(
            error_window,
            text=message,
            font=ctk.CTkFont(size=12),
            wraplength=350
        )
        message_label.pack(pady=30)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            error_window,
            text="موافق",
            command=error_window.destroy,
            fg_color="red",
            hover_color="darkred"
        )
        close_btn.pack(pady=20)

    def show_success_dialog(self, title, message):
        """عرض نافذة نجاح"""
        success_window = ctk.CTkToplevel(self)
        success_window.title(title)
        success_window.geometry("400x200")
        success_window.transient(self)
        success_window.grab_set()

        # رسالة النجاح
        message_label = ctk.CTkLabel(
            success_window,
            text=message,
            font=ctk.CTkFont(size=12),
            wraplength=350,
            text_color="green"
        )
        message_label.pack(pady=30)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            success_window,
            text="موافق",
            command=success_window.destroy,
            fg_color="green",
            hover_color="darkgreen"
        )
        close_btn.pack(pady=20)

    def confirm_delete_supplier(self):
        # Utiliser le fournisseur sélectionné
        if not hasattr(self, 'selected_supplier_id') or self.selected_supplier_id is None:
            print("Aucun fournisseur sélectionné")
            return

        # Trouver le fournisseur sélectionné
        suppliers_data = self.get_all_suppliers()
        supplier = None
        for s in suppliers_data:
            if s[0] == self.selected_supplier_id:
                supplier = s
                break

        if not supplier:
            print("Fournisseur sélectionné introuvable")
            return

        supplier_id, nom = supplier[0], supplier[1]

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_supplier_win') and self.confirm_delete_supplier_win.winfo_exists():
            self.confirm_delete_supplier_win.focus()
            return

        self.confirm_delete_supplier_win = ctk.CTkToplevel(self)
        self.confirm_delete_supplier_win.title("Confirmer la suppression")
        self.confirm_delete_supplier_win.geometry("400x200")
        self.confirm_delete_supplier_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_supplier_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer le fournisseur:\n{nom}?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM fournisseurs WHERE id = ?", (supplier_id,))
            conn.commit()
            conn.close()
            print(f"Fournisseur {nom} supprimé avec succès!")

            # إعادة ترقيم جميع الموردين تلقائياً
            self.renumber_all_suppliers()

            self.confirm_delete_supplier_win.destroy()
            self.show_suppliers_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_supplier_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def confirm_delete_invoice(self):
        # Utiliser la facture sélectionnée
        if not hasattr(self, 'selected_invoice_id') or self.selected_invoice_id is None:
            print("Aucune facture sélectionnée")
            return

        # Trouver la facture sélectionnée
        invoices_data = self.get_all_invoices()
        invoice = None
        for inv in invoices_data:
            if inv[0] == self.selected_invoice_id:
                invoice = inv
                break

        if not invoice:
            print("Facture sélectionnée introuvable")
            return

        invoice_id, numero = invoice[0], invoice[1]

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_invoice_win') and self.confirm_delete_invoice_win.winfo_exists():
            self.confirm_delete_invoice_win.focus()
            return

        self.confirm_delete_invoice_win = ctk.CTkToplevel(self)
        self.confirm_delete_invoice_win.title("Confirmer la suppression")
        self.confirm_delete_invoice_win.geometry("400x200")
        self.confirm_delete_invoice_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_invoice_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer la facture:\n{numero}?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM factures WHERE id = ?", (invoice_id,))
            conn.commit()
            conn.close()
            print(f"Facture {numero} supprimée avec succès!")

            self.confirm_delete_invoice_win.destroy()
            self.show_invoices_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_invoice_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def confirm_delete_delivery_note(self):
        # Utiliser le bon de livraison sélectionné
        if not hasattr(self, 'selected_delivery_note_id') or self.selected_delivery_note_id is None:
            print("Aucun bon de livraison sélectionné")
            return

        # Trouver le bon de livraison sélectionné dans les données d'exemple
        # Puisque nous utilisons des données d'exemple, nous allons simuler la suppression
        print(f"Confirmation de suppression du bon de livraison ID: {self.selected_delivery_note_id}")

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_dn_win') and self.confirm_delete_dn_win.winfo_exists():
            self.confirm_delete_dn_win.focus()
            return

        self.confirm_delete_dn_win = ctk.CTkToplevel(self)
        self.confirm_delete_dn_win.title("Confirmer la suppression")
        self.confirm_delete_dn_win.geometry("400x200")
        self.confirm_delete_dn_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_dn_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer ce bon de livraison?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            # Simulation de la suppression (pas de base de données réelle pour les bons de livraison)
            print(f"Bon de livraison ID {self.selected_delivery_note_id} supprimé avec succès!")

            self.confirm_delete_dn_win.destroy()
            self.show_delivery_notes_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_dn_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def confirm_delete_purchase_order(self):
        # Utiliser le bon de commande sélectionné
        if not hasattr(self, 'selected_purchase_order_id') or self.selected_purchase_order_id is None:
            print("Aucun bon de commande sélectionné")
            return

        # Simulation de la suppression (données d'exemple)
        print(f"Confirmation de suppression du bon de commande ID: {self.selected_purchase_order_id}")

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_po_win') and self.confirm_delete_po_win.winfo_exists():
            self.confirm_delete_po_win.focus()
            return

        self.confirm_delete_po_win = ctk.CTkToplevel(self)
        self.confirm_delete_po_win.title("Confirmer la suppression")
        self.confirm_delete_po_win.geometry("400x200")
        self.confirm_delete_po_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_po_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer ce bon de commande?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            # Simulation de la suppression
            print(f"Bon de commande ID {self.selected_purchase_order_id} supprimé avec succès!")

            self.confirm_delete_po_win.destroy()
            self.show_purchase_orders_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_po_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def confirm_delete_marche(self):
        # Utiliser le marché sélectionné
        if not hasattr(self, 'selected_marche_id') or self.selected_marche_id is None:
            print("Aucun marché sélectionné")
            return

        # Trouver le marché sélectionné dans les données d'exemple
        marche_reference = f"MAR{self.selected_marche_id:03d}"  # Format MAR001, MAR002, etc.
        print(f"Confirmation de suppression du marché: {marche_reference}")

        # Créer une boîte de dialogue de confirmation
        if hasattr(self, 'confirm_delete_marche_win') and self.confirm_delete_marche_win.winfo_exists():
            self.confirm_delete_marche_win.focus()
            return

        self.confirm_delete_marche_win = ctk.CTkToplevel(self)
        self.confirm_delete_marche_win.title("Confirmer la suppression")
        self.confirm_delete_marche_win.geometry("400x200")
        self.confirm_delete_marche_win.transient(self)

        frame = ctk.CTkFrame(self.confirm_delete_marche_win)
        frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Message de confirmation
        message_label = ctk.CTkLabel(frame, text=f"Êtes-vous sûr de vouloir supprimer le marché:\n{marche_reference}?",
                                   font=ctk.CTkFont(size=14), wraplength=300)
        message_label.pack(pady=20)

        warning_label = ctk.CTkLabel(frame, text="Cette action est irréversible!",
                                   font=ctk.CTkFont(size=12), text_color="red")
        warning_label.pack(pady=5)

        # Boutons
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            # Simulation de la suppression (pas de base de données réelle pour les marchés)
            print(f"Marché {marche_reference} supprimé avec succès!")

            self.confirm_delete_marche_win.destroy()
            self.show_marche_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="Supprimer", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="Annuler", command=self.confirm_delete_marche_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def select_client(self, client_id):
        """Sélectionner un client dans le tableau"""
        self.selected_client_id = client_id
        print(f"Client {client_id} sélectionné")

    def select_product(self, product_id):
        """Sélectionner un produit dans le tableau"""
        self.selected_product_id = product_id
        print(f"Produit {product_id} sélectionné")

    def select_supplier(self, supplier_id):
        """Sélectionner un fournisseur dans le tableau"""
        self.selected_supplier_id = supplier_id
        print(f"Fournisseur {supplier_id} sélectionné")

    def select_invoice(self, invoice_id):
        """Sélectionner une facture dans le tableau"""
        self.selected_invoice_id = invoice_id
        print(f"Facture {invoice_id} sélectionnée")

    def select_purchase_order(self, purchase_order_id):
        """Sélectionner un bon de commande dans le tableau"""
        self.selected_purchase_order_id = purchase_order_id
        print(f"Bon de commande {purchase_order_id} sélectionné")

    def select_delivery_note(self, delivery_note_id):
        """Sélectionner un bon de livraison dans le tableau"""
        self.selected_delivery_note_id = delivery_note_id
        print(f"Bon de livraison {delivery_note_id} sélectionné")

    def select_stock_item(self, stock_item_id):
        """Sélectionner un élément de stock dans le tableau"""
        self.selected_stock_item_id = stock_item_id
        print(f"Élément de stock {stock_item_id} sélectionné")

    def show_add_marche_dialog(self):
        """Afficher la boîte de dialogue pour ajouter un marché"""
        print("Ouverture de la boîte de dialogue d'ajout de marché...")
        # À implémenter - pour l'instant juste un message

    def show_add_devis_dialog(self):
        """Afficher la boîte de dialogue pour ajouter un devis"""
        if hasattr(self, 'add_devis_win') and self.add_devis_win.winfo_exists():
            self.add_devis_win.focus()
            return

        self.add_devis_win = ctk.CTkToplevel(self)
        self.add_devis_win.title("devis")
        self.add_devis_win.geometry("1200x800")
        self.add_devis_win.transient(self)

        # Frame principal
        main_frame = ctk.CTkScrollableFrame(self.add_devis_win)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Section client avec layout amélioré
        client_frame = ctk.CTkFrame(main_frame)
        client_frame.pack(fill="x", pady=5)

        # Configuration des colonnes pour un meilleur alignement
        client_frame.grid_columnconfigure(1, weight=2)  # Colonne des champs
        client_frame.grid_columnconfigure(2, weight=0)  # Colonne des *
        client_frame.grid_columnconfigure(3, weight=3)  # Colonne des notes

        # Type de client
        ctk.CTkLabel(client_frame, text="type de client", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, padx=10, pady=8, sticky="w")
        self.type_client_combo = ctk.CTkComboBox(client_frame, values=["Privé", "Public"], width=200)
        self.type_client_combo.grid(row=0, column=1, padx=10, pady=8, sticky="w")
        self.type_client_combo.set("Privé")
        ctk.CTkLabel(client_frame, text="*", text_color="red", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=2, padx=5, pady=8)
        ctk.CTkLabel(client_frame, text="ne pas afficher l'hors de l'impression", text_color="red", font=ctk.CTkFont(size=11)).grid(row=0, column=3, padx=10, pady=8, sticky="w")

        # Nature de prestation
        ctk.CTkLabel(client_frame, text="nature de prestation", font=ctk.CTkFont(weight="bold")).grid(row=1, column=0, padx=10, pady=8, sticky="w")
        self.nature_combo = ctk.CTkComboBox(client_frame, values=["Travaux", "fourniture"], width=200)
        self.nature_combo.grid(row=1, column=1, padx=10, pady=8, sticky="w")
        self.nature_combo.set("Travaux")
        ctk.CTkLabel(client_frame, text="*", text_color="red", font=ctk.CTkFont(size=16, weight="bold")).grid(row=1, column=2, padx=5, pady=8)
        ctk.CTkLabel(client_frame, text="ne pas afficher l'hors de l'impression", text_color="red", font=ctk.CTkFont(size=11)).grid(row=1, column=3, padx=10, pady=8, sticky="w")

        # CLIENT
        ctk.CTkLabel(client_frame, text="CLIENT", font=ctk.CTkFont(weight="bold")).grid(row=2, column=0, padx=10, pady=8, sticky="w")
        self.devis_client_combo = ctk.CTkComboBox(client_frame, values=["sélectionné CLIENT"], width=200)
        self.devis_client_combo.grid(row=2, column=1, padx=10, pady=8, sticky="w")

        # Adresse
        ctk.CTkLabel(client_frame, text="adresse", font=ctk.CTkFont(weight="bold")).grid(row=3, column=0, padx=10, pady=8, sticky="w")
        self.devis_adresse_entry = ctk.CTkEntry(client_frame, placeholder_text="si le type de client est privé afficher l'adresse automatique si non ligne grise", width=300)
        self.devis_adresse_entry.grid(row=3, column=1, padx=10, pady=8, sticky="w")
        ctk.CTkLabel(client_frame, text="*", text_color="red", font=ctk.CTkFont(size=16, weight="bold")).grid(row=3, column=2, padx=5, pady=8)
        ctk.CTkLabel(client_frame, text="afficher l'hors de l'impression en cas de client privé", text_color="red", font=ctk.CTkFont(size=11)).grid(row=3, column=3, padx=10, pady=8, sticky="w")

        # ICE
        ctk.CTkLabel(client_frame, text="ICE", font=ctk.CTkFont(weight="bold")).grid(row=4, column=0, padx=10, pady=8, sticky="w")
        self.devis_ice_entry = ctk.CTkEntry(client_frame, placeholder_text="si catégorie est privé taper le N si non ligne grise", width=200)
        self.devis_ice_entry.grid(row=4, column=1, padx=10, pady=8, sticky="w")
        ctk.CTkLabel(client_frame, text="*", text_color="red", font=ctk.CTkFont(size=16, weight="bold")).grid(row=4, column=2, padx=5, pady=8)
        ctk.CTkLabel(client_frame, text="afficher l'hors de l'impression en cas de client privé", text_color="red", font=ctk.CTkFont(size=11)).grid(row=4, column=3, padx=10, pady=8, sticky="w")

        # Objet
        ctk.CTkLabel(client_frame, text="objet", font=ctk.CTkFont(weight="bold")).grid(row=5, column=0, padx=10, pady=8, sticky="w")
        self.objet_combo = ctk.CTkComboBox(client_frame, values=["sélectionné ou manuel"], width=200)
        self.objet_combo.grid(row=5, column=1, padx=10, pady=8, sticky="w")

        # Container pour la section produits avec layout horizontal
        products_container = ctk.CTkFrame(main_frame)
        products_container.pack(fill="both", expand=True, pady=15)

        # Frame gauche pour le tableau des produits
        products_frame = ctk.CTkFrame(products_container)
        products_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # Frame droite pour les boutons ACTION
        products_right_frame = ctk.CTkFrame(products_container)
        products_right_frame.pack(side="right", fill="y", padx=(10, 0), pady=10)

        # أزرار ACTION
        ctk.CTkLabel(products_right_frame, text="ACTION", font=ctk.CTkFont(weight="bold", size=14)).pack(pady=10)

        ctk.CTkButton(products_right_frame, text="Modifier", width=120, height=30,
                     fg_color="#2d5a87", hover_color="#1e3d5c").pack(pady=5, padx=10)

        ctk.CTkButton(products_right_frame, text="Supprimer", width=120, height=30,
                     fg_color="#d32f2f", hover_color="#b71c1c").pack(pady=5, padx=10)

        ctk.CTkButton(products_right_frame, text="Dupliquer", width=120, height=30,
                     fg_color="#2d5a87", hover_color="#1e3d5c").pack(pady=5, padx=10)

        # مساحة فارغة
        ctk.CTkLabel(products_right_frame, text="").pack(expand=True)

        # زر AJOUTER ARTICLE في الأسفل
        ctk.CTkButton(products_right_frame, text="AJOUTER ARTICLE", width=140, height=35,
                     fg_color="#1f538d", hover_color="#14375e").pack(pady=20, padx=10)

        # En-têtes du tableau avec style amélioré
        headers = ["N°", "designation", "U", "qte", "prix achat ht", "marge", "prix HT", "prix total ht"]
        header_widths = [50, 200, 80, 80, 120, 80, 120, 120]

        for i, (header, width) in enumerate(zip(headers, header_widths)):
            label = ctk.CTkLabel(products_frame, text=header, font=ctk.CTkFont(weight="bold", size=12))
            label.grid(row=0, column=i, padx=8, pady=8, sticky="w")

        # Stockage des widgets pour les calculs
        self.product_widgets = []

        # Ligne 1
        ctk.CTkLabel(products_frame, text="1").grid(row=1, column=0, padx=8, pady=5)
        ctk.CTkComboBox(products_frame, values=["sélectionné produit"], width=150).grid(row=1, column=1, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="automatique").grid(row=1, column=2, padx=8, pady=5)
        ctk.CTkEntry(products_frame, placeholder_text="taper", width=80).grid(row=1, column=3, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="automatique").grid(row=1, column=4, padx=8, pady=5)
        ctk.CTkEntry(products_frame, placeholder_text="ex 1.2", width=80).grid(row=1, column=5, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="prix achat ht x marge", font=ctk.CTkFont(size=10)).grid(row=1, column=6, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="prix de vente HT", font=ctk.CTkFont(size=10)).grid(row=1, column=7, padx=8, pady=5)

        # Ligne 2
        ctk.CTkLabel(products_frame, text="2").grid(row=2, column=0, padx=8, pady=5)
        ctk.CTkComboBox(products_frame, values=["sélectionné produit"], width=150).grid(row=2, column=1, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="automatique").grid(row=2, column=2, padx=8, pady=5)
        ctk.CTkEntry(products_frame, placeholder_text="taper", width=80).grid(row=2, column=3, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="automatique").grid(row=2, column=4, padx=8, pady=5)
        ctk.CTkEntry(products_frame, placeholder_text="ex 1.1", width=80).grid(row=2, column=5, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="prix achat ht x marge", font=ctk.CTkFont(size=10)).grid(row=2, column=6, padx=8, pady=5)
        ctk.CTkLabel(products_frame, text="prix de vente HT", font=ctk.CTkFont(size=10)).grid(row=2, column=7, padx=8, pady=5)

        # Section totaux avec calculs automatiques
        totals_frame = ctk.CTkFrame(main_frame)
        totals_frame.pack(fill="x", pady=15)

        # Texte arrêté avec variable
        self.arrete_label = ctk.CTkLabel(totals_frame, text="Arrêté le présent devis à la somme de : EX ( CINQ MILLIONS CENT UN MILLE HT )", font=ctk.CTkFont(size=12, weight="bold"))
        self.arrete_label.pack(pady=15)

        # Totaux à droite avec calculs
        totals_right_frame = ctk.CTkFrame(totals_frame)
        totals_right_frame.pack(side="right", padx=30, pady=15)

        # Labels des totaux
        self.total_ht_label = ctk.CTkLabel(totals_right_frame, text="total HT: 0.00 DH", font=ctk.CTkFont(size=14, weight="bold"))
        self.total_ht_label.grid(row=0, column=0, padx=15, pady=5, sticky="e")

        self.tva_label = ctk.CTkLabel(totals_right_frame, text="TVA 20%: 0.00 DH", font=ctk.CTkFont(size=14, weight="bold"))
        self.tva_label.grid(row=1, column=0, padx=15, pady=5, sticky="e")

        self.ttc_label = ctk.CTkLabel(totals_right_frame, text="TTC: 0.00 DH", font=ctk.CTkFont(size=14, weight="bold"))
        self.ttc_label.grid(row=2, column=0, padx=15, pady=5, sticky="e")

        # Boutons en bas avec style amélioré
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=15)

        ctk.CTkButton(buttons_frame, text="valide", width=100, height=35).pack(side="left", padx=10)
        ctk.CTkButton(buttons_frame, text="supprimer", width=100, height=35).pack(side="left", padx=10)
        ctk.CTkButton(buttons_frame, text="imprimer", width=100, height=35).pack(side="left", padx=10)
        ctk.CTkLabel(buttons_frame, text="ne pas afficher l'hors de l'impression", text_color="red", font=ctk.CTkFont(size=11)).pack(side="left", padx=15)

        # Initialisation
        self.load_clients_for_devis()
        # self.load_products_for_devis()
        # self.on_devis_client_type_changed("Privé")
        # self.calculate_totals()

    def load_clients_for_devis(self):
        """Charger la liste des clients pour le devis"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, nom, type_client, adresse, ice FROM clients ORDER BY nom")
            clients = cursor.fetchall()
            conn.close()

            client_values = []
            self.clients_data = {}

            for client in clients:
                client_id, nom, type_client, adresse, ice = client
                client_values.append(nom)
                self.clients_data[nom] = {
                    'id': client_id,
                    'categorie': type_client or 'Privé',
                    'adresse': adresse or '',
                    'ice': ice or ''
                }

            if client_values:
                self.devis_client_combo.configure(values=client_values)
            else:
                self.devis_client_combo.configure(values=["Aucun client disponible"])

        except Exception as e:
            print(f"Erreur lors du chargement des clients: {e}")
            self.devis_client_combo.configure(values=["Erreur de chargement"])

    def on_client_type_changed(self, selected_type):
        """Gérer le changement de type de client (Privé/Public)"""
        if selected_type == "Privé":
            # Client privé: activer les champs adresse et ICE
            self.adresse_entry.configure(state="normal")
            self.ice_entry.configure(state="normal")
            self.adresse_entry.configure(placeholder_text="Adresse automatique du client privé")
            self.ice_entry.configure(placeholder_text="Taper le numéro ICE")
        else:  # Public
            # Client public: désactiver les champs adresse et ICE (ligne grise)
            self.adresse_entry.configure(state="disabled")
            self.ice_entry.configure(state="disabled")
            self.adresse_entry.delete(0, 'end')
            self.ice_entry.delete(0, 'end')

    def on_client_selected(self, selected_client):
        """Gérer la sélection d'un client"""
        if selected_client in self.clients_data:
            client_data = self.clients_data[selected_client]

            # Déterminer le type de client
            client_type = client_data['categorie']
            if client_type in ['Privé', 'privé', 'PRIVE']:
                self.type_client_combo.set("Privé")
                # Remplir automatiquement l'adresse et ICE pour client privé
                self.adresse_entry.delete(0, 'end')
                self.adresse_entry.insert(0, client_data['adresse'])
                self.ice_entry.delete(0, 'end')
                self.ice_entry.insert(0, client_data['ice'])
            else:
                self.type_client_combo.set("Public")

            # Appliquer les règles du type de client
            self.on_client_type_changed(self.type_client_combo.get())

    def show_devis_context_menu(self, event, row):
        """عرض قائمة السياق للتقدير"""
        if row not in self.devis_table_data:
            return

        devis_data = self.devis_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {devis_data['numero']}",
            command=lambda: self.edit_devis_from_context(devis_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {devis_data['numero']}",
            command=lambda: self.delete_devis_from_context(devis_data['id'], devis_data['numero'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_devis_context_menu(self, event):
        """عرض قائمة سياق عامة للتقديرات"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة تقدير جديد",
            command=self.show_add_devis_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_devis_from_context(self, devis_id):
        """تعديل تقدير من قائمة السياق"""
        self.selected_devis_id = devis_id
        self.edit_devis()

    def delete_devis_from_context(self, devis_id, devis_numero):
        """حذف تقدير من قائمة السياق"""
        self.selected_devis_id = devis_id
        self.delete_devis()

    def edit_devis(self):
        """تعديل تقدير"""
        print("تعديل التقدير المحدد...")
        if not hasattr(self, 'selected_devis_id') or self.selected_devis_id is None:
            print("يرجى أولاً تحديد تقدير")
            return

        devis_numero = f"DEV{self.selected_devis_id:03d}"
        print(f"تعديل التقدير {devis_numero}")
        print("نافذة تعديل التقدير مفتوحة")
        # هنا يمكن إضافة نافذة التعديل عندما تكون متوفرة

    def delete_devis(self):
        """حذف تقدير"""
        print("حذف التقدير المحدد...")
        if not hasattr(self, 'selected_devis_id') or self.selected_devis_id is None:
            print("يرجى أولاً تحديد تقدير")
            return
        self.confirm_delete_devis()

    def confirm_delete_devis(self):
        # استخدام التقدير المحدد
        if not hasattr(self, 'selected_devis_id') or self.selected_devis_id is None:
            print("لا يوجد تقدير محدد")
            return

        # إيجاد التقدير المحدد في بيانات المثال
        devis_reference = f"DEV{self.selected_devis_id:03d}"  # Format DEV001, DEV002, etc.
        print(f"تأكيد حذف التقدير: {devis_reference}")

        # إنشاء مربع حوار تأكيد
        if hasattr(self, 'confirm_delete_devis_win') and self.confirm_delete_devis_win.winfo_exists():
            self.confirm_delete_devis_win.focus()
            return

        self.confirm_delete_devis_win = ctk.CTkToplevel(self)
        self.confirm_delete_devis_win.title("تأكيد الحذف")
        self.confirm_delete_devis_win.geometry("400x200")
        self.confirm_delete_devis_win.transient(self)
        self.confirm_delete_devis_win.grab_set()

        # محتوى النافذة
        frame = ctk.CTkFrame(self.confirm_delete_devis_win)
        frame.pack(fill="both", expand=True, padx=20, pady=20)

        # رسالة التأكيد
        message_label = ctk.CTkLabel(frame, text=f"هل أنت متأكد من حذف التقدير {devis_reference}؟",
                                   font=ctk.CTkFont(size=14))
        message_label.pack(pady=20)

        # الأزرار
        buttons_frame = ctk.CTkFrame(frame)
        buttons_frame.pack(pady=20)

        def delete_confirmed():
            # محاكاة الحذف (لا توجد قاعدة بيانات حقيقية للتقديرات)
            print(f"تقدير {devis_reference} تم حذفه بنجاح!")

            self.confirm_delete_devis_win.destroy()
            self.show_devis_page()

        confirm_button = ctk.CTkButton(buttons_frame, text="حذف", command=delete_confirmed,
                                     fg_color="red", hover_color="darkred")
        confirm_button.pack(side="left", padx=10)

        cancel_button = ctk.CTkButton(buttons_frame, text="إلغاء", command=self.confirm_delete_devis_win.destroy)
        cancel_button.pack(side="left", padx=10)

    def create_marche_table(self, parent):
        """Créer le tableau des marchés"""
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Référence", "Objet", "Montant", "Statut", "Date création"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        # جلب بيانات الصفقات من قاعدة البيانات
        marches_data = self.get_all_marches()

        # حفظ بيانات الصفقات لاستخدامها في قائمة السياق
        self.marches_table_data = {}

        for row_idx, marche in enumerate(marches_data, start=1):
            marche_id, type_marche, devis_numero, objet, client, montant_ttc, statut, date_creation = marche

            # إنشاء مرجع للصفقة
            reference = f"MAR{marche_id:03d}"
            montant_display = f"{montant_ttc:.2f} DH" if montant_ttc else "0.00 DH"
            date_display = date_creation[:10] if date_creation else ""

            # حفظ بيانات الصفقة
            self.marches_table_data[row_idx] = {
                'id': marche_id,
                'reference': reference,
                'objet': objet,
                'montant': montant_display,
                'statut': statut,
                'date_creation': date_display
            }

            # عرض بيانات الصفقة
            display_values = [reference, objet, montant_display, statut, date_display]

            for col_idx, value in enumerate(display_values):
                cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

                # إضافة قائمة السياق للخلية
                cell.bind("<Button-3>", lambda e, row=row_idx: self.show_marche_context_menu(e, row))

        # إضافة قائمة السياق للإطار أيضاً
        table_frame.bind("<Button-3>", self.show_general_marches_context_menu)

    def get_all_marches(self):
        """جلب جميع الصفقات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, type_marche, devis_numero, objet, client,
                       montant_ttc, statut, date_creation
                FROM marches
                ORDER BY date_creation DESC
            ''')
            marches = cursor.fetchall()
            conn.close()
            return marches
        except Exception as e:
            print(f"❌ خطأ في جلب الصفقات: {e}")
            return []

    def show_marche_context_menu(self, event, row):
        """عرض قائمة السياق للمشروع"""
        if row not in self.marches_table_data:
            return

        marche_data = self.marches_table_data[row]

        # إنشاء قائمة السياق
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        # إضافة عناصر القائمة
        context_menu.add_command(
            label=f"✏️ تعديل {marche_data['reference']}",
            command=lambda: self.edit_marche_from_context(marche_data['id'])
        )

        context_menu.add_separator()

        context_menu.add_command(
            label=f"🗑️ حذف {marche_data['reference']}",
            command=lambda: self.delete_marche_from_context(marche_data['id'], marche_data['reference'])
        )

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_general_marches_context_menu(self, event):
        """عرض قائمة سياق عامة للمشاريع"""
        import tkinter as tk
        context_menu = tk.Menu(self, tearoff=0)

        context_menu.add_command(
            label=f"➕ إضافة مشروع جديد",
            command=self.show_add_marche_dialog
        )

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def edit_marche_from_context(self, marche_id):
        """تعديل مشروع من قائمة السياق"""
        self.selected_marche_id = marche_id
        self.edit_marche()

    def delete_marche_from_context(self, marche_id, marche_reference):
        """حذف مشروع من قائمة السياق"""
        self.selected_marche_id = marche_id
        self.delete_marche()

    def edit_marche(self):
        """تعديل صفقة"""
        if not hasattr(self, 'selected_marche_id') or self.selected_marche_id is None:
            print("يرجى أولاً تحديد صفقة")
            return

        self.show_edit_marche_dialog()

    def show_edit_marche_dialog(self):
        """عرض نافذة تعديل الصفقة"""
        if hasattr(self, 'edit_marche_win') and self.edit_marche_win.winfo_exists():
            self.edit_marche_win.focus()
            return

        # جلب بيانات الصفقة الحالية
        marche_data = self.get_marche_by_id(self.selected_marche_id)
        if not marche_data:
            print("لم يتم العثور على بيانات الصفقة")
            return

        self.edit_marche_win = ctk.CTkToplevel(self)
        self.edit_marche_win.title(f"تعديل الصفقة MAR{self.selected_marche_id:03d}")
        self.edit_marche_win.geometry("900x700")
        self.edit_marche_win.transient(self)

        # إطار قابل للتمرير
        main_frame = ctk.CTkScrollableFrame(self.edit_marche_win)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = ctk.CTkLabel(main_frame, text=f"تعديل الصفقة MAR{self.selected_marche_id:03d}",
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=(0, 20))

        # إنشاء الأقسام مع البيانات الحالية
        self.create_edit_marche_sections(main_frame, marche_data)

        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        save_button = ctk.CTkButton(buttons_frame, text=self.language_manager.get_text("save"),
                                  command=self.update_marche, fg_color="green")
        save_button.pack(side="right", padx=5)

        cancel_button = ctk.CTkButton(buttons_frame, text=self.language_manager.get_text("cancel"),
                                    command=self.edit_marche_win.destroy)
        cancel_button.pack(side="right", padx=5)

    def create_edit_marche_sections(self, parent, marche_data):
        """إنشاء أقسام تعديل الصفقة مع البيانات الحالية"""
        # قسم المعلومات العامة
        general_frame = ctk.CTkFrame(parent)
        general_frame.pack(fill="x", pady=10)

        section_title = ctk.CTkLabel(general_frame, text=self.language_manager.get_text("marche_general_info"),
                                   font=ctk.CTkFont(size=16, weight="bold"))
        section_title.pack(anchor="w", padx=10, pady=(10, 5))

        # إطار الحقول
        fields_frame = ctk.CTkFrame(general_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1), weight=1)

        # حفظ مراجع الحقول لاستخدامها عند الحفظ
        self.edit_marche_fields = {}

        # ملء الحقول بالبيانات الحالية
        self.edit_marche_fields['type_marche'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("type_marche"), marche_data[1], 0, 0)
        self.edit_marche_fields['devis_numero'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("devis_numero"), marche_data[2], 0, 1)
        self.edit_marche_fields['numero_manuel'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("numero_manuel"), marche_data[3], 1, 0)
        self.edit_marche_fields['nature_prestation'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("nature_prestation"), marche_data[4], 1, 1)
        self.edit_marche_fields['objet'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("objet"), marche_data[5], 2, 0)
        self.edit_marche_fields['delai_execution'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("delai_execution"), marche_data[6], 2, 1)
        self.edit_marche_fields['client'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("client"), marche_data[7], 3, 0)
        self.edit_marche_fields['montant_ttc'] = self.create_labeled_entry_with_value(
            fields_frame, self.language_manager.get_text("montant_ttc"), str(marche_data[8] or 0), 3, 1)

    def create_labeled_entry_with_value(self, parent, label_text, current_value, row, col):
        """إنشاء حقل مع تسمية وقيمة حالية"""
        label = ctk.CTkLabel(parent, text=label_text, anchor="w")
        label.grid(row=row*2, column=col, padx=5, pady=(5, 2), sticky="w")

        entry = ctk.CTkEntry(parent)
        entry.insert(0, current_value or "")
        entry.grid(row=row*2+1, column=col, padx=5, pady=(0, 5), sticky="ew")

        return entry

    def get_marche_by_id(self, marche_id):
        """جلب بيانات صفقة محددة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, type_marche, devis_numero, numero_manuel, nature_prestation,
                       objet, delai_execution, client, montant_ttc, statut, date_creation
                FROM marches WHERE id = ?
            ''', (marche_id,))
            marche = cursor.fetchone()
            conn.close()
            return marche
        except Exception as e:
            print(f"❌ خطأ في جلب بيانات الصفقة: {e}")
            return None

    def update_marche(self):
        """تحديث بيانات الصفقة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جمع البيانات المحدثة
            type_marche = self.edit_marche_fields['type_marche'].get()
            devis_numero = self.edit_marche_fields['devis_numero'].get()
            numero_manuel = self.edit_marche_fields['numero_manuel'].get()
            nature_prestation = self.edit_marche_fields['nature_prestation'].get()
            objet = self.edit_marche_fields['objet'].get()
            delai_execution = self.edit_marche_fields['delai_execution'].get()
            client = self.edit_marche_fields['client'].get()
            montant_ttc = float(self.edit_marche_fields['montant_ttc'].get() or 0)

            # تحديث الصفقة
            cursor.execute('''
                UPDATE marches SET
                    type_marche = ?, devis_numero = ?, numero_manuel = ?,
                    nature_prestation = ?, objet = ?, delai_execution = ?,
                    client = ?, montant_ttc = ?
                WHERE id = ?
            ''', (type_marche, devis_numero, numero_manuel, nature_prestation,
                  objet, delai_execution, client, montant_ttc, self.selected_marche_id))

            conn.commit()
            conn.close()

            print(f"✅ تم تحديث الصفقة MAR{self.selected_marche_id:03d} بنجاح!")

            # إغلاق النافذة وتحديث العرض
            self.edit_marche_win.destroy()
            self.show_marche_page()

        except ValueError as e:
            print(f"❌ خطأ في القيم: {e}")
        except Exception as e:
            print(f"❌ خطأ في تحديث الصفقة: {e}")

    def delete_marche(self):
        """حذف مشروع"""
        print("حذف المشروع المحدد...")
        if not hasattr(self, 'selected_marche_id') or self.selected_marche_id is None:
            print("يرجى أولاً تحديد مشروع")
            return
        self.confirm_delete_marche()

    def select_marche(self, marche_id):
        """Sélectionner un marché dans le tableau"""
        self.selected_marche_id = marche_id
        print(f"Marché {marche_id} sélectionné")

    def select_cash_movement(self, cash_movement_id):
        """Sélectionner un mouvement de caisse dans le tableau"""
        self.selected_cash_movement_id = cash_movement_id
        print(f"Mouvement de caisse {cash_movement_id} sélectionné")

    def select_recent_invoice(self, invoice_id):
        """Sélectionner une facture récente dans le tableau de bord"""
        self.selected_recent_invoice_id = invoice_id
        print(f"Facture récente {invoice_id} sélectionnée")

    def show_add_marche_dialog(self):
        """عرض نافذة إضافة صفقة جديدة"""
        if hasattr(self, 'add_marche_win') and self.add_marche_win.winfo_exists():
            self.add_marche_win.focus()
            return

        self.add_marche_win = ctk.CTkToplevel(self)
        self.add_marche_win.title(self.language_manager.get_text("nouveau_marche"))
        self.add_marche_win.geometry("900x700")
        self.add_marche_win.transient(self)

        # إطار قابل للتمرير
        main_frame = ctk.CTkScrollableFrame(self.add_marche_win)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = ctk.CTkLabel(main_frame, text=self.language_manager.get_text("nouveau_marche"),
                                 font=ctk.CTkFont(size=20, weight="bold"))
        title_label.pack(pady=(0, 20))

        # قسم المعلومات العامة - 14 حقل
        self.create_marche_general_info_section(main_frame)

        # قسم بنود الأسعار - 9 أعمدة
        self.create_marche_bordereau_section(main_frame)

        # قسم التكاليف الإضافية - 10 حقول
        self.create_marche_frais_section(main_frame)

        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=20)

        save_button = ctk.CTkButton(buttons_frame, text=self.language_manager.get_text("save"),
                                  command=self.save_new_marche, fg_color="green")
        save_button.pack(side="right", padx=5)

        cancel_button = ctk.CTkButton(buttons_frame, text=self.language_manager.get_text("cancel"),
                                    command=self.add_marche_win.destroy)
        cancel_button.pack(side="right", padx=5)









    def save_new_marche(self):
        """حفظ الصفقة الجديدة في قاعدة البيانات حسب المواصفات الجديدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جمع بيانات المعلومات العامة (14 حقل)
            type_marche = self.marche_fields['type_marche'].get() or "DC"
            devis_numero = self.marche_fields['devis_numero'].get() or ""
            numero_manuel = self.marche_fields['numero_manuel'].get() or ""
            nature_prestation = self.marche_fields['nature_prestation'].get() or "Travaux"
            objet = self.marche_fields['objet'].get() or ""
            delai_execution = self.marche_fields['delai_execution'].get() or ""
            client_name = self.marche_fields['client'].get() or ""
            montant_ttc = float(self.marche_fields['montant_ttc'].get() or 0)
            caution_provisoire = float(self.marche_fields['caution_provisoire'].get() or 0)
            date_notification = self.marche_fields['date_notification_approbation'].get() or None
            caution_definitif = float(self.marche_fields['caution_definitif'].get() or 0)
            ordre_service = self.marche_fields['ordre_service'].get() or None
            caution_retenu = self.marche_fields['caution_retenu_garantie'].get() or "0"
            date_achevement = self.marche_fields['date_prevu_achevement'].get() or None

            # جلب client_id من اسم العميل
            client_id = None
            if client_name:
                cursor.execute("SELECT id FROM clients WHERE nom = ?", (client_name,))
                client_result = cursor.fetchone()
                if client_result:
                    client_id = client_result[0]

            # إدراج الصفقة في قاعدة البيانات
            cursor.execute('''
                INSERT INTO marches (
                    type_marche, devis_numero, numero_manuel, nature_prestation,
                    objet, delai_execution, client_id, montant_ttc, caution_provisoire,
                    date_notification_approbation, caution_definitif, ordre_service,
                    caution_retenu_garantie, date_prevu_achevement
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (type_marche, devis_numero, numero_manuel, nature_prestation,
                  objet, delai_execution, client_id, montant_ttc, caution_provisoire,
                  date_notification, caution_definitif, ordre_service,
                  caution_retenu, date_achevement))

            marche_id = cursor.lastrowid

            # إضافة بنود الأسعار (Bordereau de Prix) - 9 أعمدة
            if hasattr(self, 'bordereau_rows'):
                for row_num, row_data in self.bordereau_rows.items():
                    try:
                        designation = row_data[1]['entry'].get()  # Désignation
                        if not designation or designation == "AUTOMATIQUE":
                            continue

                        unite = row_data[2]['entry'].get() or "unité"  # U
                        quantite = float(row_data[3]['entry'].get() or 0)  # Qte
                        prix_achat_ht = float(row_data[4]['entry'].get() or 0)  # PRIX ACHAT HT
                        total_achat_ht = float(row_data[5]['entry'].get() or 0)  # TOTAL ACHAT HT
                        prix_u_ht = float(row_data[6]['entry'].get() or 0)  # Prix U HT
                        total_vente_ht = float(row_data[7]['entry'].get() or 0)  # TOTAL VENT HT
                        marge = float(row_data[8]['entry'].get() or 0)  # MARGE

                        cursor.execute('''
                            INSERT INTO marche_items (
                                marche_id, numero, designation, unite, quantite,
                                prix_achat_ht, total_achat_ht, prix_u_ht, total_vente_ht, marge
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (marche_id, row_num, designation, unite, quantite,
                              prix_achat_ht, total_achat_ht, prix_u_ht, total_vente_ht, marge))

                    except Exception as e:
                        print(f"❌ خطأ في حفظ بند رقم {row_num}: {e}")
                        continue

            # إضافة التكاليف الإضافية (Transport & Autres) - 10 حقول
            frais_config = [
                ('TRANSPORT', '500'),
                ('Courrier', '501'),
                ('Autres 1', '502'),
                ('Autres 2', '503'),
                ('Autres 3', '504'),
                ('Autres 4', '505'),
                ('Autres 5', '506'),
                ('Autres 6', '507'),
                ('Autres 7', '508'),
                ('Reste bénéficiaire', 'AUTO')
            ]

            # حفظ التكاليف الإضافية (مبسط للاختبار)
            if hasattr(self, 'frais_fields'):
                for frais_type, code in frais_config:
                    try:
                        # هذا مبسط - يحتاج تطوير لاحقاً
                        montant = 0  # قيمة افتراضية
                        cursor.execute('''
                            INSERT INTO marche_frais (marche_id, type_frais, code_comptable, montant)
                            VALUES (?, ?, ?, ?)
                        ''', (marche_id, frais_type, code, montant))
                    except Exception as e:
                        print(f"❌ خطأ في حفظ تكلفة {frais_type}: {e}")

            conn.commit()
            conn.close()

            print(f"✅ تم حفظ الصفقة بنجاح! ID: {marche_id}")

            # إغلاق النافذة وتحديث العرض
            self.add_marche_win.destroy()
            self.show_marche_page()

        except ValueError as e:
            print(f"❌ خطأ في القيم: {e}")
        except Exception as e:
            print(f"❌ خطأ في حفظ الصفقة: {e}")

    # ===== NOUVELLES FONCTIONS POUR BON DE LIVRAISON AMÉLIORÉ =====

    def show_enhanced_delivery_note_dialog(self):
        """عرض نافذة بون دو ليفريزون محسن"""
        if hasattr(self, 'enhanced_bl_win') and self.enhanced_bl_win.winfo_exists():
            self.enhanced_bl_win.focus()
            return

        self.enhanced_bl_win = ctk.CTkToplevel(self)
        self.enhanced_bl_win.title("BON DE LIVRAISON")
        self.enhanced_bl_win.geometry("900x600")
        self.enhanced_bl_win.transient(self)

        # إطار رئيسي
        main_frame = ctk.CTkFrame(self.enhanced_bl_win)
        main_frame.pack(expand=True, fill="both", padx=20, pady=20)

        # العنوان
        title_label = ctk.CTkLabel(main_frame, text="BON DE LIVRAISON",
                                 font=ctk.CTkFont(size=18, weight="bold"))
        title_label.pack(pady=(0, 20))

        # معلومات أساسية
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 20))

        # الصف الأول
        row1 = ctk.CTkFrame(info_frame)
        row1.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(row1, text="N BON DE LIVRAISON:").pack(side="left", padx=(0, 10))
        self.bl_numero_entry = ctk.CTkEntry(row1, placeholder_text="automatique")
        self.bl_numero_entry.pack(side="left", padx=(0, 20))

        ctk.CTkLabel(row1, text="Type de marché:").pack(side="left", padx=(0, 10))
        self.bl_type_combo = ctk.CTkComboBox(row1, values=["BC", "marché"])
        self.bl_type_combo.pack(side="left")

        # الصف الثاني
        row2 = ctk.CTkFrame(info_frame)
        row2.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(row2, text="DEVIS N°:").pack(side="left", padx=(0, 10))

        # إنشاء ComboBox قابل للبحث للتقديرات
        self.bl_devis_combo = ctk.CTkComboBox(
            row2,
            values=self.get_devis_list(),
            command=self.on_devis_selected,
            width=250
        )
        self.bl_devis_combo.pack(side="left", padx=(0, 20))

        # إضافة وظيفة البحث المباشر
        self.bl_devis_combo.bind('<KeyRelease>', self.on_devis_search)
        self.bl_devis_combo.bind('<Button-1>', self.on_devis_click)

        ctk.CTkLabel(row2, text="CLIENT:").pack(side="left", padx=(0, 10))
        self.bl_client_combo = ctk.CTkComboBox(row2, values=self.get_clients_list())
        self.bl_client_combo.pack(side="left")

        # الصف الثالث
        row3 = ctk.CTkFrame(info_frame)
        row3.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(row3, text="ICE:").pack(side="left", padx=(0, 10))
        self.bl_ice_entry = ctk.CTkEntry(row3, placeholder_text="si catégorie et privé")
        self.bl_ice_entry.pack(side="left", padx=(0, 20))

        ctk.CTkLabel(row3, text="adresse:").pack(side="left", padx=(0, 10))
        self.bl_adresse_entry = ctk.CTkEntry(row3, placeholder_text="adresse de livraison", width=300)
        self.bl_adresse_entry.pack(side="left")

        # جدول التفاصيل
        details_frame = ctk.CTkFrame(main_frame)
        details_frame.pack(fill="both", expand=True, pady=(0, 20))

        ctk.CTkLabel(details_frame, text="détailles", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=10)

        # جدول بسيط - حفظ مرجع للوصول إليه لاحقاً
        self.details_table_frame = ctk.CTkScrollableFrame(details_frame)
        self.details_table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        table_frame = self.details_table_frame  # للتوافق مع الكود الموجود

        headers = ["N°", "designation", "U", "qte", "reste au BC/MARCHE", "RESTE EN STOCK"]
        for i, header in enumerate(headers):
            ctk.CTkLabel(table_frame, text=header, font=ctk.CTkFont(weight="bold")).grid(row=0, column=i, padx=5, pady=5)

        # صفوف افتراضية
        for row in range(1, 3):
            ctk.CTkLabel(table_frame, text=str(row)).grid(row=row, column=0, padx=5, pady=2)
            ctk.CTkEntry(table_frame, placeholder_text="AUTOMATIQUE", width=150).grid(row=row, column=1, padx=5, pady=2)
            ctk.CTkComboBox(table_frame, values=["automatique", "AUTO/manuel"], width=100).grid(row=row, column=2, padx=5, pady=2)
            ctk.CTkComboBox(table_frame, values=["AUTO/manuel"], width=100).grid(row=row, column=3, padx=5, pady=2)
            ctk.CTkEntry(table_frame, placeholder_text="qte livré", width=120).grid(row=row, column=4, padx=5, pady=2)
            ctk.CTkEntry(table_frame, placeholder_text="", width=120).grid(row=row, column=5, padx=5, pady=2)

        # الأزرار
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x")

        ctk.CTkButton(buttons_frame, text="validé", fg_color="green",
                     command=self.save_enhanced_bl).pack(side="right", padx=10, pady=10)
        ctk.CTkButton(buttons_frame, text="supprimer", fg_color="red",
                     command=self.delete_enhanced_bl).pack(side="right", padx=10, pady=10)
        ctk.CTkButton(buttons_frame, text="imprimer", fg_color="blue",
                     command=self.print_enhanced_bl).pack(side="right", padx=10, pady=10)

        # توليد رقم تلقائي
        self.generate_bl_numero()

    def get_clients_list(self):
        """الحصول على قائمة العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT client FROM clients ORDER BY client")
            clients = [row[0] for row in cursor.fetchall()]
            conn.close()
            return ["automatique"] + clients
        except:
            return ["automatique", "Client A", "Client B"]

    def get_devis_list(self):
        """الحصول على قائمة التقديرات (devis)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف وإعادة إنشاء جدول devis محسن مع جميع الحقول المطلوبة
            try:
                cursor.execute("DROP TABLE IF EXISTS devis")
                print("✅ تم حذف جدول devis القديم")
            except:
                pass

            cursor.execute("""
                CREATE TABLE devis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT NOT NULL,
                    client TEXT,
                    date_creation TEXT,
                    montant_ht REAL,
                    tva REAL,
                    montant_ttc REAL,
                    statut TEXT DEFAULT 'En attente',
                    objet TEXT,
                    adresse_livraison TEXT,
                    ice_client TEXT,
                    type_marche TEXT DEFAULT 'BC',
                    numero_bl_auto TEXT,
                    numero_manuel TEXT,
                    nature_prestation TEXT,
                    delai_execution TEXT,
                    caution_provisoire REAL,
                    date_notification TEXT,
                    caution_definitif REAL,
                    ordre_service TEXT,
                    caution_retenu REAL,
                    date_achevement TEXT
                )
            """)
            print("✅ تم إنشاء جدول devis محسن")

            # إدراج بيانات تجريبية إذا كان الجدول فارغاً
            cursor.execute("SELECT COUNT(*) FROM devis")
            if cursor.fetchone()[0] == 0:
                sample_devis = [
                    ("DEV001", "Client A", "2024-01-15", 10000.00, 2000.00, 12000.00, "En attente",
                     "Fourniture matériel informatique", "123 Rue Example", "ICE001", "BC",
                     "BL202501-001", "001", "Fourniture", "30 jours", 1200.00, "2024-02-01",
                     2400.00, "OS001", 600.00, "2024-03-15"),

                    ("DEV002", "Client B", "2024-01-10", 5000.00, 1000.00, 6000.00, "Accepté",
                     "Services de maintenance", "456 Avenue Test", "ICE002", "marché",
                     "BL202501-002", "002", "Services", "15 jours", 600.00, "2024-01-25",
                     1200.00, "OS002", 300.00, "2024-02-10"),

                    ("DEV003", "Client C", "2024-01-20", 15000.00, 3000.00, 18000.00, "En cours",
                     "Installation réseau", "789 Boulevard Demo", "ICE003", "BC",
                     "BL202501-003", "003", "Installation", "45 jours", 1800.00, "2024-02-15",
                     3600.00, "OS003", 900.00, "2024-04-01")
                ]

                for devis in sample_devis:
                    cursor.execute("""
                        INSERT INTO devis (numero, client, date_creation, montant_ht, tva, montant_ttc, statut,
                                         objet, adresse_livraison, ice_client, type_marche, numero_bl_auto,
                                         numero_manuel, nature_prestation, delai_execution, caution_provisoire,
                                         date_notification, caution_definitif, ordre_service, caution_retenu, date_achevement)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, devis)

            # تحميل قائمة التقديرات
            cursor.execute("SELECT numero, client FROM devis ORDER BY numero")
            devis_list = cursor.fetchall()
            conn.commit()
            conn.close()

            # تنسيق القائمة لعرض رقم التقدير واسم العميل
            formatted_list = [f"{numero} - {client}" for numero, client in devis_list]
            return ["SELECTIONNE OU TAPER"] + formatted_list

        except Exception as e:
            print(f"⚠️ خطأ في تحميل التقديرات: {e}")
            return ["SELECTIONNE OU TAPER", "DEV001 - Client A", "DEV002 - Client B", "DEV003 - Client C"]

    def on_devis_selected(self, selected_devis):
        """عند اختيار devis - ملء المعلومات تلقائياً"""
        if not selected_devis or selected_devis == "SELECTIONNE OU TAPER":
            return

        try:
            # استخراج رقم التقدير من النص المختار
            devis_numero = selected_devis.split(" - ")[0]

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل جميع بيانات التقدير لملء جميع الحقول "automatique"
            cursor.execute("""
                SELECT client, objet, adresse_livraison, ice_client, montant_ttc, type_marche,
                       numero_bl_auto, numero_manuel, nature_prestation, delai_execution,
                       caution_provisoire, date_notification, caution_definitif, ordre_service,
                       caution_retenu, date_achevement
                FROM devis WHERE numero = ?
            """, (devis_numero,))

            result = cursor.fetchone()
            conn.close()

            if result:
                (client, objet, adresse_livraison, ice_client, montant_ttc, type_marche,
                 numero_bl_auto, numero_manuel, nature_prestation, delai_execution,
                 caution_provisoire, date_notification, caution_definitif, ordre_service,
                 caution_retenu, date_achevement) = result

                # ملء جميع الحقول "automatique" من بيانات DEVIS

                # 1. N BON DE LIVRAISON (automatique) - من numero_bl_auto
                if hasattr(self, 'bl_numero_entry') and numero_bl_auto:
                    self.bl_numero_entry.delete(0, 'end')
                    self.bl_numero_entry.insert(0, numero_bl_auto)

                # 2. Type de marché - من type_marche
                if hasattr(self, 'bl_type_combo') and type_marche:
                    self.bl_type_combo.set(type_marche)

                # 3. CLIENT (automatique) - من client
                if hasattr(self, 'bl_client_combo') and client:
                    # البحث عن العميل في القائمة وتحديده
                    client_values = self.bl_client_combo.cget("values")
                    for value in client_values:
                        if client in value:
                            self.bl_client_combo.set(value)
                            break
                    else:
                        # إذا لم يوجد في القائمة، أضفه وحدده
                        current_values = list(client_values)
                        current_values.append(client)
                        self.bl_client_combo.configure(values=current_values)
                        self.bl_client_combo.set(client)

                # 4. ICE (si catégorie et privé) - من ice_client
                if hasattr(self, 'bl_ice_entry') and ice_client:
                    self.bl_ice_entry.delete(0, 'end')
                    self.bl_ice_entry.insert(0, ice_client)

                # 5. adresse (si catégorie et privé) - من adresse_livraison
                if hasattr(self, 'bl_adresse_entry') and adresse_livraison:
                    self.bl_adresse_entry.delete(0, 'end')
                    self.bl_adresse_entry.insert(0, adresse_livraison)

                # 6. ملء جدول التفاصيل - جميع الحقول "AUTOMATIQUE"
                self.fill_details_from_devis(devis_numero, objet, nature_prestation)

                print(f"✅ تم ملء جميع الحقول 'automatique' من التقدير {devis_numero}")
                print(f"   العميل: {client}")
                print(f"   نوع السوق: {type_marche}")
                print(f"   رقم BL: {numero_bl_auto}")
                print(f"   طبيعة الخدمة: {nature_prestation}")
                print(f"   العنوان: {adresse_livraison}")
                print(f"   ICE: {ice_client}")
                print(f"   المبلغ: {montant_ttc} DH")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات التقدير: {e}")

    def fill_details_from_devis(self, devis_numero, objet, nature_prestation):
        """ملء جدول التفاصيل من بيانات DEVIS - جميع الحقول 'AUTOMATIQUE'"""
        try:
            # الحصول على عناصر الجدول الموجودة
            if not hasattr(self, 'details_table_frame'):
                return

            # مسح البيانات القديمة وملء بيانات جديدة من DEVIS

            # البحث عن جميع عناصر الجدول التي تحتوي على "AUTOMATIQUE"
            for widget in self.details_table_frame.winfo_children():
                if isinstance(widget, ctk.CTkEntry):
                    placeholder = widget.cget("placeholder_text")
                    if placeholder and "AUTOMATIQUE" in placeholder:
                        # ملء الحقل ببيانات من DEVIS
                        widget.delete(0, 'end')

                        # تحديد نوع الحقل وملء البيانات المناسبة
                        if "designation" in str(widget.grid_info().get('column', '')):
                            # حقل designation - ملء بالموضوع أو طبيعة الخدمة
                            widget.insert(0, objet or nature_prestation or f"خدمات {devis_numero}")
                        else:
                            # حقول أخرى - ملء بقيم افتراضية
                            widget.insert(0, f"من {devis_numero}")

                elif isinstance(widget, ctk.CTkComboBox):
                    values = widget.cget("values")
                    if values and any("automatique" in str(v).lower() for v in values):
                        # تحديد قيمة "automatique" في ComboBox
                        for value in values:
                            if "automatique" in str(value).lower():
                                widget.set(value)
                                break

            print(f"✅ تم ملء جدول التفاصيل من {devis_numero}")

        except Exception as e:
            print(f"⚠️ خطأ في ملء جدول التفاصيل: {e}")

            # طريقة بديلة - ملء الصفوف الافتراضية
            self.fill_default_table_rows(devis_numero, objet, nature_prestation)

    def fill_default_table_rows(self, devis_numero, objet, nature_prestation):
        """طريقة بديلة لملء صفوف الجدول الافتراضية"""
        try:
            # إذا لم تنجح الطريقة الأولى، نقوم بملء البيانات يدوياً

            # بيانات افتراضية للجدول من DEVIS
            default_rows = [
                {
                    "numero": "1",
                    "designation": objet or f"خدمات {devis_numero}",
                    "unite": "automatique",
                    "qte": "AUTO/manuel",
                    "reste_bc": f"من {devis_numero}",
                    "reste_stock": "AUTO"
                },
                {
                    "numero": "2",
                    "designation": nature_prestation or f"مواد {devis_numero}",
                    "unite": "automatique",
                    "qte": "AUTO/manuel",
                    "reste_bc": f"من {devis_numero}",
                    "reste_stock": "AUTO"
                }
            ]

            print(f"✅ تم تحضير بيانات افتراضية للجدول من {devis_numero}")

            # عرض البيانات في الكونسول
            for i, row in enumerate(default_rows, 1):
                print(f"   الصف {i}: {row['designation']} - {row['unite']} - {row['qte']}")

        except Exception as e:
            print(f"⚠️ خطأ في الطريقة البديلة: {e}")

    def on_devis_search(self, event):
        """بحث مباشر في قائمة التقديرات"""
        try:
            # الحصول على النص المكتوب
            search_text = self.bl_devis_combo.get().lower()

            if len(search_text) < 2:  # بدء البحث بعد حرفين
                return

            # تحميل جميع التقديرات
            all_devis = self.get_devis_list()

            # فلترة النتائج حسب النص المبحوث عنه
            filtered_devis = [devis for devis in all_devis if search_text in devis.lower()]

            # تحديث قائمة الخيارات
            if filtered_devis:
                self.bl_devis_combo.configure(values=filtered_devis)
            else:
                # إذا لم توجد نتائج، عرض جميع التقديرات
                self.bl_devis_combo.configure(values=all_devis)

        except Exception as e:
            print(f"⚠️ خطأ في البحث: {e}")

    def on_devis_click(self, event):
        """عند النقر على ComboBox - عرض جميع الخيارات"""
        try:
            # إعادة تحميل جميع التقديرات
            all_devis = self.get_devis_list()
            self.bl_devis_combo.configure(values=all_devis)
        except Exception as e:
            print(f"⚠️ خطأ في تحميل القائمة: {e}")

    def generate_bl_numero(self):
        """توليد رقم بون دو ليفريزون"""
        import datetime
        date_str = datetime.datetime.now().strftime("%Y%m")
        numero = f"BL{date_str}-001"
        if hasattr(self, 'bl_numero_entry'):
            self.bl_numero_entry.delete(0, 'end')
            self.bl_numero_entry.insert(0, numero)

    def save_enhanced_bl(self):
        """حفظ بون دو ليفريزون"""
        try:
            self.run_bl_migration()

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            numero = self.bl_numero_entry.get() or "BL-001"
            type_marche = self.bl_type_combo.get() or "BC"
            devis_number = self.bl_devis_combo.get() if self.bl_devis_combo.get() != "SELECTIONNE OU TAPER" else ""
            client = self.bl_client_combo.get()
            ice_client = self.bl_ice_entry.get()
            adresse = self.bl_adresse_entry.get()

            cursor.execute("""
                INSERT INTO bons_livraison
                (numero, date_creation, type_marche, devis_number, ice_client, adresse_livraison, statut)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (numero, datetime.datetime.now().strftime("%Y-%m-%d"),
                  type_marche, devis_number, ice_client, adresse, "En cours"))

            conn.commit()
            conn.close()

            print(f"✅ Bon de livraison {numero} créé avec succès!")
            self.enhanced_bl_win.destroy()
            self.show_delivery_notes_page()

        except Exception as e:
            print(f"❌ Erreur: {e}")

    def delete_enhanced_bl(self):
        """حذف بون دو ليفريزون"""
        print("🗑️ Suppression")
        self.enhanced_bl_win.destroy()

    def print_enhanced_bl(self):
        """طباعة بون دو ليفريزون"""
        print("🖨️ Impression")

    def run_bl_migration(self):
        """تشغيل migration"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bons_livraison (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT NOT NULL,
                    date_creation TEXT NOT NULL,
                    type_marche TEXT DEFAULT 'BC',
                    devis_number TEXT,
                    ice_client TEXT,
                    adresse_livraison TEXT,
                    statut TEXT DEFAULT 'En cours'
                )
            """)

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"⚠️ Migration error: {e}")

    def create_enhanced_delivery_notes_table(self, parent):
        """جدول بونات التسليم"""
        table_frame = ctk.CTkScrollableFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ["Numéro BL", "Type", "Client", "Date", "Statut"]

        for i, col in enumerate(columns):
            header = ctk.CTkLabel(table_frame, text=col, font=ctk.CTkFont(weight="bold"))
            header.grid(row=0, column=i, padx=10, pady=5, sticky="w")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT numero, type_marche, ice_client, date_creation, statut
                FROM bons_livraison
                ORDER BY date_creation DESC
            """)
            bls = cursor.fetchall()
            conn.close()

            for row_idx, bl in enumerate(bls, start=1):
                for col_idx, value in enumerate(bl):
                    cell = ctk.CTkLabel(table_frame, text=str(value) if value else "-", anchor="w")
                    cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

        except Exception as e:
            print(f"⚠️ Erreur: {e}")
            # بيانات تجريبية
            sample_data = [
                ("BL202501-001", "BC", "Client A", "2025-01-15", "En cours"),
                ("BL202501-002", "marché", "Client B", "2025-01-16", "Livré")
            ]

            for row_idx, data in enumerate(sample_data, start=1):
                for col_idx, value in enumerate(data):
                    cell = ctk.CTkLabel(table_frame, text=value, anchor="w")
                    cell.grid(row=row_idx, column=col_idx, padx=10, pady=2, sticky="w")

def main():
    """Fonction principale pour lancer l'application"""
    print("🚀 Démarrage du Système de Comptabilité Avancé...")
    print("📋 CustomTkinter - Interface Belle et Moderne")
    print("=" * 50)

    try:
        # Vérification des dépendances requises
        if not check_dependencies():
            return 1

        # Configuration du chemin de la base de données
        print(f"📁 Répertoire de la base de données: {DB_PATH}")

        # Définition de l'apparence et des couleurs
        ctk.set_appearance_mode("dark")  # "light", "dark", "system"
        ctk.set_default_color_theme("dark-blue")  # "blue", "green", "dark-blue"

        print("🎯 Lancement de l'application...")
        app = AccountingApp()
        app.mainloop()

        print("✅ Application fermée avec succès")
        return 0

    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        print("💡 Veuillez vérifier l'installation des bibliothèques requises")
        return 1

    except Exception as e:
        print(f"❌ Erreur dans l'application: {e}")
        print("🔧 Détails de l'erreur:")
        traceback.print_exc()
        return 1



if __name__ == "__main__":
    # Lancement de l'application et retour du code de sortie
    exit_code = main()
    sys.exit(exit_code)
