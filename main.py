#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import datetime
from pathlib import Path
import customtkinter as ctk
from tkinter import ttk, messagebox
import tkinter as tk

# إعداد المسار النسبي لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DB_PATH.mkdir(exist_ok=True)
DATABASE_FILE = DB_PATH / "accounting.db"

class ModernAccountingApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # إعداد النافذة الرئيسية
        self.title("Système Comptable Moderne")
        self.geometry("1600x1000")
        self.state('zoomed')  # ملء الشاشة
        
        # إعداد الألوان والمظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إعداد قاعدة البيانات
        self.db_path = str(DATABASE_FILE)
        self.setup_database()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # عرض لوحة القيادة افتراضياً
        self.show_dashboard()
    
    def setup_database(self):
        """إعداد قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                nom TEXT NOT NULL,
                type_client TEXT DEFAULT 'Privé',
                adresse TEXT,
                ice TEXT,
                telephone TEXT,
                email TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                designation TEXT NOT NULL,
                unite TEXT DEFAULT 'unité',
                prix_achat REAL DEFAULT 0,
                prix_vente REAL DEFAULT 0,
                stock INTEGER DEFAULT 0,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fournisseurs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                nom TEXT NOT NULL,
                adresse TEXT,
                telephone TEXT,
                ice TEXT,
                date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS factures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE,
                client_id INTEGER,
                date_facture DATE,
                montant_ht REAL DEFAULT 0,
                tva REAL DEFAULT 20,
                montant_ttc REAL DEFAULT 0,
                statut TEXT DEFAULT 'En attente',
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')
        
        # جدول عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS devis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT UNIQUE,
                client_id INTEGER,
                type_client TEXT DEFAULT 'Privé',
                nature_prestation TEXT DEFAULT 'Travaux',
                objet TEXT,
                date_devis DATE,
                montant_ht REAL DEFAULT 0,
                tva REAL DEFAULT 20,
                montant_ttc REAL DEFAULT 0,
                statut TEXT DEFAULT 'En attente',
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')
        
        # جدول بنود عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS devis_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                devis_id INTEGER,
                numero INTEGER,
                designation TEXT,
                unite TEXT DEFAULT 'unité',
                quantite REAL DEFAULT 1,
                prix_achat_ht REAL DEFAULT 0,
                marge REAL DEFAULT 0,
                prix_ht REAL DEFAULT 0,
                prix_total_ht REAL DEFAULT 0,
                FOREIGN KEY (devis_id) REFERENCES devis (id) ON DELETE CASCADE
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Base de données initialisée avec succès")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد الشبكة الرئيسية
        self.grid_columnconfigure(0, weight=0)  # الشريط الجانبي
        self.grid_columnconfigure(1, weight=1)  # المحتوى الرئيسي
        self.grid_rowconfigure(0, weight=1)
        
        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء المنطقة الرئيسية
        self.main_frame = ctk.CTkFrame(self, corner_radius=0)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        self.sidebar = ctk.CTkFrame(self, width=280, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_rowconfigure(20, weight=1)
        self.sidebar.grid_propagate(False)
        
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            self.sidebar, 
            text="💼 Système Comptable",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=(30, 20))
        
        # أزرار التنقل
        nav_buttons = [
            ("🏠 Tableau de Bord", self.show_dashboard),
            ("👥 Clients", self.show_clients),
            ("🏭 Fournisseurs", self.show_suppliers),
            ("📦 Produits", self.show_products),
            ("📋 Devis", self.show_devis),
            ("🧾 Factures", self.show_invoices),
            ("📝 Bons de Commande", self.show_purchase_orders),
            ("🚚 Bons de Livraison", self.show_delivery_notes),
            ("🏗️ Marchés", self.show_marches),
            ("📊 Stock", self.show_stock),
            ("💰 Caisse", self.show_cash),
            ("📈 TVA", self.show_tva),
            ("📊 Rapports", self.show_reports),
            ("🔍 Recherche", self.show_search)
        ]
        
        self.nav_buttons = {}
        for i, (text, command) in enumerate(nav_buttons, start=1):
            btn = ctk.CTkButton(
                self.sidebar,
                text=text,
                command=command,
                height=45,
                font=ctk.CTkFont(size=14),
                anchor="w",
                fg_color="transparent",
                text_color=("gray10", "gray90"),
                hover_color=("gray80", "gray20")
            )
            btn.grid(row=i, column=0, padx=20, pady=5, sticky="ew")
            self.nav_buttons[text] = btn
    
    def clear_main_content(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
    
    def set_active_button(self, active_text):
        """تعيين الزر النشط"""
        for text, btn in self.nav_buttons.items():
            if text == active_text:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color="transparent")
    
    def show_dashboard(self):
        """عرض لوحة القيادة"""
        self.clear_main_content()
        self.set_active_button("🏠 Tableau de Bord")
        
        # عنوان الصفحة
        title = ctk.CTkLabel(
            self.main_frame,
            text="📊 Tableau de Bord",
            font=ctk.CTkFont(size=32, weight="bold")
        )
        title.pack(pady=30)
        
        # بطاقات الإحصائيات
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.pack(fill="x", padx=30, pady=20)
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # إحصائيات سريعة
        self.create_stat_card(stats_frame, "👥 Clients", self.get_clients_count(), "#3b82f6", 0)
        self.create_stat_card(stats_frame, "📦 Produits", self.get_products_count(), "#10b981", 1)
        self.create_stat_card(stats_frame, "🧾 Factures", self.get_invoices_count(), "#f59e0b", 2)
        self.create_stat_card(stats_frame, "🏭 Fournisseurs", self.get_suppliers_count(), "#ef4444", 3)
    
    def create_stat_card(self, parent, title, value, color, column):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent)
        card.grid(row=0, column=column, padx=15, pady=15, sticky="ew")
        
        title_label = ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(pady=(20, 5))
        
        value_label = ctk.CTkLabel(card, text=str(value), font=ctk.CTkFont(size=28, weight="bold"))
        value_label.pack(pady=(5, 20))
    
    def get_clients_count(self):
        """الحصول على عدد العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM clients")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0
    
    def get_products_count(self):
        """الحصول على عدد المنتجات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM produits")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0
    
    def get_invoices_count(self):
        """الحصول على عدد الفواتير"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM factures")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0
    
    def get_suppliers_count(self):
        """الحصول على عدد الموردين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    def show_clients(self):
        """عرض صفحة العملاء"""
        self.clear_main_content()
        self.set_active_button("👥 Clients")

        # عنوان الصفحة
        header_frame = ctk.CTkFrame(self.main_frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        title = ctk.CTkLabel(header_frame, text="👥 Gestion des Clients", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(side="left", padx=20, pady=15)

        add_btn = ctk.CTkButton(header_frame, text="➕ Nouveau Client", command=self.add_client_dialog)
        add_btn.pack(side="right", padx=20, pady=15)

        # جدول العملاء
        self.create_clients_table()

    def show_suppliers(self):
        """عرض صفحة الموردين"""
        self.clear_main_content()
        self.set_active_button("🏭 Fournisseurs")

        # عنوان الصفحة
        header_frame = ctk.CTkFrame(self.main_frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        title = ctk.CTkLabel(header_frame, text="🏭 Gestion des Fournisseurs", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(side="left", padx=20, pady=15)

        add_btn = ctk.CTkButton(header_frame, text="➕ Nouveau Fournisseur", command=self.add_supplier_dialog)
        add_btn.pack(side="right", padx=20, pady=15)

        # جدول الموردين
        self.create_suppliers_table()

    def show_products(self):
        """عرض صفحة المنتجات"""
        self.clear_main_content()
        self.set_active_button("📦 Produits")

        # عنوان الصفحة
        header_frame = ctk.CTkFrame(self.main_frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        title = ctk.CTkLabel(header_frame, text="📦 Gestion des Produits", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(side="left", padx=20, pady=15)

        add_btn = ctk.CTkButton(header_frame, text="➕ Nouveau Produit", command=self.add_product_dialog)
        add_btn.pack(side="right", padx=20, pady=15)

        # جدول المنتجات
        self.create_products_table()

    def show_devis(self):
        """عرض صفحة عروض الأسعار"""
        self.clear_main_content()
        self.set_active_button("📋 Devis")

        # عنوان الصفحة
        header_frame = ctk.CTkFrame(self.main_frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        title = ctk.CTkLabel(header_frame, text="📋 Gestion des Devis", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(side="left", padx=20, pady=15)

        add_btn = ctk.CTkButton(header_frame, text="➕ Nouveau Devis", command=self.add_devis_dialog)
        add_btn.pack(side="right", padx=20, pady=15)

        # جدول عروض الأسعار
        self.create_devis_table()

    def show_invoices(self):
        """عرض صفحة الفواتير"""
        self.clear_main_content()
        self.set_active_button("🧾 Factures")

        # عنوان الصفحة
        header_frame = ctk.CTkFrame(self.main_frame)
        header_frame.pack(fill="x", padx=20, pady=20)

        title = ctk.CTkLabel(header_frame, text="🧾 Gestion des Factures", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(side="left", padx=20, pady=15)

        add_btn = ctk.CTkButton(header_frame, text="➕ Nouvelle Facture", command=self.add_invoice_dialog)
        add_btn.pack(side="right", padx=20, pady=15)

        # جدول الفواتير
        self.create_invoices_table()

    def show_purchase_orders(self):
        """عرض صفحة أوامر الشراء"""
        self.clear_main_content()
        self.set_active_button("📝 Bons de Commande")

        title = ctk.CTkLabel(self.main_frame, text="📝 Bons de Commande", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_delivery_notes(self):
        """عرض صفحة إيصالات التسليم"""
        self.clear_main_content()
        self.set_active_button("🚚 Bons de Livraison")

        title = ctk.CTkLabel(self.main_frame, text="🚚 Bons de Livraison", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_marches(self):
        """عرض صفحة المناقصات"""
        self.clear_main_content()
        self.set_active_button("🏗️ Marchés")

        title = ctk.CTkLabel(self.main_frame, text="🏗️ Marchés", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_stock(self):
        """عرض صفحة المخزون"""
        self.clear_main_content()
        self.set_active_button("📊 Stock")

        title = ctk.CTkLabel(self.main_frame, text="📊 Gestion du Stock", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_cash(self):
        """عرض صفحة الصندوق"""
        self.clear_main_content()
        self.set_active_button("💰 Caisse")

        title = ctk.CTkLabel(self.main_frame, text="💰 Gestion de la Caisse", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_tva(self):
        """عرض صفحة ضريبة القيمة المضافة"""
        self.clear_main_content()
        self.set_active_button("📈 TVA")

        title = ctk.CTkLabel(self.main_frame, text="📈 Gestion de la TVA", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_reports(self):
        """عرض صفحة التقارير"""
        self.clear_main_content()
        self.set_active_button("📊 Rapports")

        title = ctk.CTkLabel(self.main_frame, text="📊 Rapports et Statistiques", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def show_search(self):
        """عرض صفحة البحث"""
        self.clear_main_content()
        self.set_active_button("🔍 Recherche")

        title = ctk.CTkLabel(self.main_frame, text="🔍 Recherche Avancée", font=ctk.CTkFont(size=24, weight="bold"))
        title.pack(pady=50)

        info = ctk.CTkLabel(self.main_frame, text="قريباً - سيتم إضافة هذا القسم", font=ctk.CTkFont(size=16))
        info.pack()

    def create_clients_table(self):
        """إنشاء جدول العملاء"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إنشاء Treeview للجدول
        columns = ("Code", "Nom", "Type", "Téléphone", "Email", "Date Création")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # تحميل البيانات
        self.load_clients_data(tree)

    def create_suppliers_table(self):
        """إنشاء جدول الموردين"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("Code", "Nom", "Téléphone", "Adresse", "ICE", "Date Création")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.load_suppliers_data(tree)

    def create_products_table(self):
        """إنشاء جدول المنتجات"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("Code", "Désignation", "Unité", "Prix Achat", "Prix Vente", "Stock")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.load_products_data(tree)

    def create_devis_table(self):
        """إنشاء جدول عروض الأسعار"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("Numéro", "Client", "Type", "Date", "Montant HT", "TVA", "Montant TTC", "Statut")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط النقر المزدوج لفتح نافذة التعديل
        tree.bind("<Double-1>", lambda e: self.edit_devis_dialog(tree))

        self.load_devis_data(tree)

    def create_invoices_table(self):
        """إنشاء جدول الفواتير"""
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)

        columns = ("Numéro", "Client", "Date", "Montant HT", "TVA", "Montant TTC", "Statut")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=130)

        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.load_invoices_data(tree)

    def load_clients_data(self, tree):
        """تحميل بيانات العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT code, nom, type_client, telephone, email, date_creation FROM clients ORDER BY id DESC")
            rows = cursor.fetchall()

            for row in tree.get_children():
                tree.delete(row)

            for row in rows:
                tree.insert("", "end", values=row)

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات العملاء: {e}")

    def load_suppliers_data(self, tree):
        """تحميل بيانات الموردين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT code, nom, telephone, adresse, ice, date_creation FROM fournisseurs ORDER BY id DESC")
            rows = cursor.fetchall()

            for row in tree.get_children():
                tree.delete(row)

            for row in rows:
                tree.insert("", "end", values=row)

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات الموردين: {e}")

    def load_products_data(self, tree):
        """تحميل بيانات المنتجات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT code, designation, unite, prix_achat, prix_vente, stock FROM produits ORDER BY id DESC")
            rows = cursor.fetchall()

            for row in tree.get_children():
                tree.delete(row)

            for row in rows:
                tree.insert("", "end", values=row)

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات المنتجات: {e}")

    def load_devis_data(self, tree):
        """تحميل بيانات عروض الأسعار"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT d.numero, c.nom, d.type_client, d.date_devis,
                       d.montant_ht, d.tva, d.montant_ttc, d.statut
                FROM devis d
                LEFT JOIN clients c ON d.client_id = c.id
                ORDER BY d.id DESC
            """)
            rows = cursor.fetchall()

            for row in tree.get_children():
                tree.delete(row)

            for row in rows:
                tree.insert("", "end", values=row)

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات عروض الأسعار: {e}")

    def load_invoices_data(self, tree):
        """تحميل بيانات الفواتير"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT f.numero, c.nom, f.date_facture,
                       f.montant_ht, f.tva, f.montant_ttc, f.statut
                FROM factures f
                LEFT JOIN clients c ON f.client_id = c.id
                ORDER BY f.id DESC
            """)
            rows = cursor.fetchall()

            for row in tree.get_children():
                tree.delete(row)

            for row in rows:
                tree.insert("", "end", values=row)

            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات الفواتير: {e}")

    def add_client_dialog(self):
        """نافذة إضافة عميل جديد"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()

        # الحقول
        fields = {}

        ctk.CTkLabel(dialog, text="معلومات العميل", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=20)

        # إطار الحقول
        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # الحقول
        field_names = [
            ("الكود:", "code"),
            ("الاسم:", "nom"),
            ("نوع العميل:", "type_client"),
            ("العنوان:", "adresse"),
            ("ICE:", "ice"),
            ("الهاتف:", "telephone"),
            ("البريد الإلكتروني:", "email")
        ]

        for i, (label, key) in enumerate(field_names):
            ctk.CTkLabel(fields_frame, text=label).grid(row=i, column=0, padx=10, pady=10, sticky="w")

            if key == "type_client":
                fields[key] = ctk.CTkComboBox(fields_frame, values=["Privé", "Public"])
                fields[key].set("Privé")
            else:
                fields[key] = ctk.CTkEntry(fields_frame, width=300)

            fields[key].grid(row=i, column=1, padx=10, pady=10, sticky="ew")

        # أزرار
        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_client():
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO clients (code, nom, type_client, adresse, ice, telephone, email)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    fields["code"].get(),
                    fields["nom"].get(),
                    fields["type_client"].get(),
                    fields["adresse"].get(),
                    fields["ice"].get(),
                    fields["telephone"].get(),
                    fields["email"].get()
                ))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح!")
                dialog.destroy()
                self.show_clients()  # تحديث الجدول

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العميل: {e}")

        ctk.CTkButton(buttons_frame, text="حفظ", command=save_client).pack(side="right", padx=5)
        ctk.CTkButton(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side="right", padx=5)

    def add_supplier_dialog(self):
        """نافذة إضافة مورد جديد"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("إضافة مورد جديد")
        dialog.geometry("500x350")
        dialog.transient(self)
        dialog.grab_set()

        fields = {}

        ctk.CTkLabel(dialog, text="معلومات المورد", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=20)

        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="both", expand=True, padx=20, pady=10)

        field_names = [
            ("الكود:", "code"),
            ("الاسم:", "nom"),
            ("الهاتف:", "telephone"),
            ("العنوان:", "adresse"),
            ("ICE:", "ice")
        ]

        for i, (label, key) in enumerate(field_names):
            ctk.CTkLabel(fields_frame, text=label).grid(row=i, column=0, padx=10, pady=10, sticky="w")
            fields[key] = ctk.CTkEntry(fields_frame, width=300)
            fields[key].grid(row=i, column=1, padx=10, pady=10, sticky="ew")

        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_supplier():
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO fournisseurs (code, nom, telephone, adresse, ice)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    fields["code"].get(),
                    fields["nom"].get(),
                    fields["telephone"].get(),
                    fields["adresse"].get(),
                    fields["ice"].get()
                ))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح!")
                dialog.destroy()
                self.show_suppliers()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المورد: {e}")

        ctk.CTkButton(buttons_frame, text="حفظ", command=save_supplier).pack(side="right", padx=5)
        ctk.CTkButton(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side="right", padx=5)

    def add_product_dialog(self):
        """نافذة إضافة منتج جديد"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("500x400")
        dialog.transient(self)
        dialog.grab_set()

        fields = {}

        ctk.CTkLabel(dialog, text="معلومات المنتج", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=20)

        fields_frame = ctk.CTkFrame(dialog)
        fields_frame.pack(fill="both", expand=True, padx=20, pady=10)

        field_names = [
            ("الكود:", "code"),
            ("التسمية:", "designation"),
            ("الوحدة:", "unite"),
            ("سعر الشراء:", "prix_achat"),
            ("سعر البيع:", "prix_vente"),
            ("المخزون:", "stock")
        ]

        for i, (label, key) in enumerate(field_names):
            ctk.CTkLabel(fields_frame, text=label).grid(row=i, column=0, padx=10, pady=10, sticky="w")

            if key == "unite":
                fields[key] = ctk.CTkComboBox(fields_frame, values=["unité", "kg", "m", "m²", "m³", "litre"])
                fields[key].set("unité")
            else:
                fields[key] = ctk.CTkEntry(fields_frame, width=300)

            fields[key].grid(row=i, column=1, padx=10, pady=10, sticky="ew")

        buttons_frame = ctk.CTkFrame(dialog)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        def save_product():
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT INTO produits (code, designation, unite, prix_achat, prix_vente, stock)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    fields["code"].get(),
                    fields["designation"].get(),
                    fields["unite"].get(),
                    float(fields["prix_achat"].get() or 0),
                    float(fields["prix_vente"].get() or 0),
                    int(fields["stock"].get() or 0)
                ))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح!")
                dialog.destroy()
                self.show_products()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {e}")

        ctk.CTkButton(buttons_frame, text="حفظ", command=save_product).pack(side="right", padx=5)
        ctk.CTkButton(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side="right", padx=5)

    def add_devis_dialog(self):
        """نافذة إضافة عرض سعر جديد - تصميم حديث مثل الصورة"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("📋 Nouveau Devis")
        dialog.geometry("1200x800")
        dialog.transient(self)
        dialog.grab_set()

        # إطار رئيسي قابل للتمرير
        main_scroll = ctk.CTkScrollableFrame(dialog)
        main_scroll.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان DEVIS
        title_label = ctk.CTkLabel(main_scroll, text="DEVIS", font=ctk.CTkFont(size=32, weight="bold"))
        title_label.pack(pady=20)

        # قسم المعلومات العامة
        info_frame = ctk.CTkFrame(main_scroll)
        info_frame.pack(fill="x", padx=20, pady=10)

        info_title = ctk.CTkLabel(info_frame, text="Informations Générales", font=ctk.CTkFont(size=16, weight="bold"))
        info_title.pack(anchor="w", padx=15, pady=(15, 5))

        # شبكة المعلومات العامة
        info_grid = ctk.CTkFrame(info_frame)
        info_grid.pack(fill="x", padx=15, pady=15)
        info_grid.grid_columnconfigure((0, 1, 2, 3), weight=1)

        self.devis_fields = {}

        # الصف الأول
        ctk.CTkLabel(info_grid, text="N°:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.devis_fields["numero"] = ctk.CTkEntry(info_grid, placeholder_text="DV0001")
        self.devis_fields["numero"].grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        ctk.CTkLabel(info_grid, text="Type de client:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.devis_fields["type_client"] = ctk.CTkComboBox(info_grid, values=["Privé", "Public"])
        self.devis_fields["type_client"].grid(row=0, column=3, padx=5, pady=5, sticky="ew")

        # الصف الثاني
        ctk.CTkLabel(info_grid, text="Nature de prestation:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.devis_fields["nature_prestation"] = ctk.CTkComboBox(info_grid, values=["Travaux", "Fourniture", "Service"])
        self.devis_fields["nature_prestation"].grid(row=1, column=1, columnspan=3, padx=5, pady=5, sticky="ew")

        # قسم معلومات العميل
        client_frame = ctk.CTkFrame(main_scroll)
        client_frame.pack(fill="x", padx=20, pady=10)

        client_title = ctk.CTkLabel(client_frame, text="Informations Client", font=ctk.CTkFont(size=16, weight="bold"))
        client_title.pack(anchor="w", padx=15, pady=(15, 5))

        client_grid = ctk.CTkFrame(client_frame)
        client_grid.pack(fill="x", padx=15, pady=15)
        client_grid.grid_columnconfigure(1, weight=1)

        # حقول العميل
        client_labels = ["CLIENT:", "Adresse:", "ICE:", "Objet:"]
        for i, label in enumerate(client_labels):
            ctk.CTkLabel(client_grid, text=label).grid(row=i, column=0, padx=5, pady=5, sticky="w")

            if label == "CLIENT:":
                self.devis_fields["client"] = ctk.CTkComboBox(client_grid, values=self.get_clients_list())
            else:
                field_name = label.lower().replace(":", "")
                self.devis_fields[field_name] = ctk.CTkEntry(client_grid)

            self.devis_fields[label.lower().replace(":", "")] = self.devis_fields.get("client", ctk.CTkEntry(client_grid))
            self.devis_fields[label.lower().replace(":", "")].grid(row=i, column=1, padx=5, pady=5, sticky="ew")

        # قسم التفاصيل (الجدول)
        details_frame = ctk.CTkFrame(main_scroll)
        details_frame.pack(fill="both", expand=True, padx=20, pady=10)

        details_title = ctk.CTkLabel(details_frame, text="Détails", font=ctk.CTkFont(size=16, weight="bold"))
        details_title.pack(anchor="w", padx=15, pady=(15, 5))

        # إنشاء جدول المنتجات
        self.create_devis_products_table(details_frame)

        # قسم المجاميع
        totals_frame = ctk.CTkFrame(main_scroll)
        totals_frame.pack(fill="x", padx=20, pady=10)

        totals_grid = ctk.CTkFrame(totals_frame)
        totals_grid.pack(side="right", padx=15, pady=15)

        # حقول المجاميع
        self.devis_fields["total_ht"] = ctk.CTkEntry(totals_grid, placeholder_text="0.00 DH")
        self.devis_fields["tva"] = ctk.CTkEntry(totals_grid, placeholder_text="20%")
        self.devis_fields["total_ttc"] = ctk.CTkEntry(totals_grid, placeholder_text="0.00 DH")

        ctk.CTkLabel(totals_grid, text="Total HT:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.devis_fields["total_ht"].grid(row=0, column=1, padx=5, pady=5)

        ctk.CTkLabel(totals_grid, text="TVA 20%:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.devis_fields["tva"].grid(row=1, column=1, padx=5, pady=5)

        ctk.CTkLabel(totals_grid, text="TTC:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.devis_fields["total_ttc"].grid(row=2, column=1, padx=5, pady=5)

        # أزرار العمل
        buttons_frame = ctk.CTkFrame(main_scroll)
        buttons_frame.pack(fill="x", padx=20, pady=20)

        ctk.CTkButton(buttons_frame, text="💾 Enregistrer", command=lambda: self.save_devis(dialog),
                     fg_color="green").pack(side="right", padx=5)
        ctk.CTkButton(buttons_frame, text="🖨️ Imprimer", fg_color="blue").pack(side="right", padx=5)
        ctk.CTkButton(buttons_frame, text="❌ Annuler", command=dialog.destroy,
                     fg_color="red").pack(side="right", padx=5)

    def create_devis_products_table(self, parent):
        """إنشاء جدول المنتجات في عرض السعر"""
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # عناوين الأعمدة
        headers = ["N°", "Désignation", "U", "Qté", "Prix achat HT", "Marge", "Prix HT", "Prix total HT"]

        # إنشاء شبكة العناوين
        for i, header in enumerate(headers):
            label = ctk.CTkLabel(table_frame, text=header, font=ctk.CTkFont(weight="bold"))
            label.grid(row=0, column=i, padx=2, pady=5, sticky="ew")

        # صفوف المنتجات (2 صف افتراضي)
        self.devis_items = []
        for row in range(1, 3):
            self.add_devis_item_row(table_frame, row)

        # أزرار إدارة الصفوف
        buttons_row = ctk.CTkFrame(table_frame)
        buttons_row.grid(row=10, column=0, columnspan=8, pady=10, sticky="ew")

        ctk.CTkButton(buttons_row, text="➕ Ajouter Ligne",
                     command=lambda: self.add_devis_item_row(table_frame, len(self.devis_items) + 1)).pack(side="left", padx=5)
        ctk.CTkButton(buttons_row, text="✏️ Modifier Ligne").pack(side="left", padx=5)
        ctk.CTkButton(buttons_row, text="🗑️ Supprimer Ligne").pack(side="left", padx=5)

    def add_devis_item_row(self, parent, row_num):
        """إضافة صف منتج جديد"""
        item_fields = {}

        # رقم البند
        item_fields["numero"] = ctk.CTkLabel(parent, text=str(row_num))
        item_fields["numero"].grid(row=row_num, column=0, padx=2, pady=2)

        # التسمية
        item_fields["designation"] = ctk.CTkComboBox(parent, values=self.get_products_list())
        item_fields["designation"].grid(row=row_num, column=1, padx=2, pady=2, sticky="ew")

        # الوحدة
        item_fields["unite"] = ctk.CTkEntry(parent, placeholder_text="automatique", width=80)
        item_fields["unite"].grid(row=row_num, column=2, padx=2, pady=2)

        # الكمية
        item_fields["quantite"] = ctk.CTkEntry(parent, placeholder_text="automatique", width=80)
        item_fields["quantite"].grid(row=row_num, column=3, padx=2, pady=2)

        # سعر الشراء
        item_fields["prix_achat"] = ctk.CTkEntry(parent, placeholder_text="ex 1.2", width=100)
        item_fields["prix_achat"].grid(row=row_num, column=4, padx=2, pady=2)

        # الهامش
        item_fields["marge"] = ctk.CTkEntry(parent, placeholder_text="ex 1.1", width=80)
        item_fields["marge"].grid(row=row_num, column=5, padx=2, pady=2)

        # السعر HT
        item_fields["prix_ht"] = ctk.CTkEntry(parent, placeholder_text="prix achat ht x marge", width=120)
        item_fields["prix_ht"].grid(row=row_num, column=6, padx=2, pady=2)

        # السعر الإجمالي
        item_fields["prix_total"] = ctk.CTkEntry(parent, placeholder_text="prix de vente HT", width=120)
        item_fields["prix_total"].grid(row=row_num, column=7, padx=2, pady=2)

        self.devis_items.append(item_fields)

    def get_clients_list(self):
        """الحصول على قائمة العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT nom FROM clients ORDER BY nom")
            clients = [row[0] for row in cursor.fetchall()]
            conn.close()
            return clients if clients else ["sélectionné CLIENT"]
        except:
            return ["sélectionné CLIENT"]

    def get_products_list(self):
        """الحصول على قائمة المنتجات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT designation FROM produits ORDER BY designation")
            products = [row[0] for row in cursor.fetchall()]
            conn.close()
            return products if products else ["sélectionné produit"]
        except:
            return ["sélectionné produit"]

    def save_devis(self, dialog):
        """حفظ عرض السعر"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حفظ عرض السعر الرئيسي
            cursor.execute("""
                INSERT INTO devis (numero, type_client, nature_prestation, objet,
                                 date_devis, montant_ht, tva, montant_ttc)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.devis_fields["numero"].get(),
                self.devis_fields["type_client"].get(),
                self.devis_fields["nature_prestation"].get(),
                self.devis_fields.get("objet", ctk.CTkEntry(dialog)).get(),
                datetime.date.today().isoformat(),
                float(self.devis_fields["total_ht"].get() or 0),
                20.0,
                float(self.devis_fields["total_ttc"].get() or 0)
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم حفظ عرض السعر بنجاح!")
            dialog.destroy()
            self.show_devis()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ عرض السعر: {e}")

    def edit_devis_dialog(self, tree):
        """تعديل عرض سعر موجود"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عرض سعر للتعديل")
            return

        # فتح نافذة التعديل (نفس نافذة الإضافة مع تعبئة البيانات)
        self.add_devis_dialog()

    def add_invoice_dialog(self):
        """نافذة إضافة فاتورة جديدة"""
        messagebox.showinfo("قريباً", "سيتم إضافة نافذة الفواتير قريباً")


def main():
    """تشغيل التطبيق"""
    print("🚀 تشغيل النظام المحاسبي الحديث...")

    try:
        app = ModernAccountingApp()
        app.mainloop()
        print("✅ تم إغلاق التطبيق بنجاح")

    except Exception as e:
        print(f"❌ خطأ في التطبيق: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
